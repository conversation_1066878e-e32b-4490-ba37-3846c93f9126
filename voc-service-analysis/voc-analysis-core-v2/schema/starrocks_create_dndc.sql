CREATE TABLE `ays_meta_data` (
                                 `id` varchar(40) NOT NULL COMMENT "主键",
                                 `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                 `data` json NULL COMMENT "",
                                 `source` varchar(10) NOT NULL COMMENT "消息来源 api,mq,file等",
                                 `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "接收时间",
                                 `operator` varchar(40) NULL COMMENT "操作人 推送数据的人或系统名称",
                                 `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0",
                                 `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                 `ext_fields` json NULL COMMENT "扩展字段",
                                 `tid` varchar(55) NULL COMMENT "链路标识"
) ENGINE=OLAP
    PRIMARY KEY(`id`)
COMMENT "数据清洗-原始数据记录表"
DISTRIBUTED BY HASH(`id`)
ORDER BY(`work_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);


CREATE TABLE `ays_batch_push_record` (
                                         `id` varchar(40) NOT NULL COMMENT "主键",
                                         `reqeut_id` varchar(40) NOT NULL COMMENT "请求处理标识",
                                         `work_id` varchar(40) NOT NULL COMMENT "处理标识",
                                         `status` varchar(5) NULL DEFAULT "0" COMMENT "0:未处理，1：已处理，-1：异常",
                                         `source` varchar(20) NULL COMMENT "A:前置过滤， B：后置过滤， C：模型已达标，D：模型未达标，E：异常,F:解析数据校验",
                                         `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                         `ext_fields` json NULL COMMENT "扩展字段",
                                         `create_time` datetime NULL COMMENT "接收时间",
                                         `update_time` datetime NULL COMMENT "更新时间",
                                         `tid` varchar(60) NULL COMMENT "链路标识"
) ENGINE=OLAP
    PRIMARY KEY(`id`)
COMMENT "数据清洗-分批次接收数据记录表"
DISTRIBUTED BY HASH(`id`)
ORDER BY(`work_id`, `reqeut_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `ays_meta_data_analysis` (
                                          `new_id` varchar(40) NOT NULL COMMENT "主键",
                                          `id` varchar(40) NOT NULL COMMENT "主键",
                                          `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                          `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                          `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                          `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                          `title` varchar(500) NULL COMMENT "标题",
                                          `content` varchar(50000) NULL COMMENT "内容",
                                          `user_name` varchar(200) NULL COMMENT "昵称",
                                          `data` json NULL COMMENT "",
                                          `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0",
                                          `data_status` int(11) NULL COMMENT "数据状态 0全部 1去噪数据 2已打标数据 3未打标数据",
                                          `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                          `ext_fields` json NULL COMMENT "扩展字段",
                                          `publish_time` datetime NULL COMMENT "发布时间",
                                          `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "接收时间"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "数据清洗-模型入参数据记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `client_id`, `done`, `create_time`, `new_id`, `id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `ays_pre_process_data` (
                                        `new_id` varchar(40) NOT NULL COMMENT "主键",
                                        `id` varchar(40) NOT NULL COMMENT "主键",
                                        `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                        `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                        `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                        `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                        `data` json NULL COMMENT "",
                                        `data_md5` json NULL COMMENT "内容md5值",
                                        `publish_time` datetime NULL COMMENT "发布时间",
                                        `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                        `ext_fields` json NULL COMMENT "扩展字段",
                                        `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "接收时间",
                                        `abandon` int(11) NULL COMMENT "是否遗弃数据 是：1，否：0",
                                        `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0",
                                        `hit_rules` json NULL COMMENT "规则id集合"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "数据清洗-前置处理后数据记录表"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `client_id`, `done`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `ays_api_reslt_data` (
                                      `new_id` varchar(40) NOT NULL COMMENT "主键",
                                      `id` varchar(40) NOT NULL COMMENT "主键",
                                      `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                      `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                      `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                      `original_id` varchar(40) NOT NULL COMMENT "原文id",
                                      `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                      `data` json NULL COMMENT "",
                                      `data_md5` json NULL COMMENT "内容md5值",
                                      `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                      `ext_fields` json NULL COMMENT "扩展字段",
                                      `publish_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "发布时间",
                                      `create_time` datetime NULL COMMENT "接收时间",
                                      `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`, `id`)
COMMENT "数据清洗-前置处理后数据记录表"
DISTRIBUTED BY HASH(`new_id`, `id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);




CREATE TABLE `ays_api_reslt_data_analysis` (
                                               `new_id` varchar(40) NOT NULL COMMENT "主键id",
                                               `id` varchar(40) NOT NULL COMMENT "主键id",
                                               `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                               `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                               `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                               `original_id` varchar(40) NOT NULL COMMENT "原文id",
                                               `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                               `input_data_id` varchar(100) NULL COMMENT "原文id",
                                               `sample_data_type` varchar(100) NULL COMMENT "是否是示例数据",
                                               `original_text_scene` varchar(10000) NULL COMMENT "原文片段",
                                               `brand_code_name` varchar(1000) NULL COMMENT "品牌名称",
                                               `car_series_name` varchar(1000) NULL COMMENT "车系名称",
                                               `b_tag` varchar(2000) NULL COMMENT "业务标签",
                                               `q_tag` varchar(2000) NULL COMMENT "质量标签",
                                               `business_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                               `business_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                               `business_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                               `business_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                               `quality_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                               `quality_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                               `quality_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                               `quality_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                               `scenario` varchar(500) NULL COMMENT "用车场景",
                                               `sentiment` varchar(1000) NULL COMMENT "情感",
                                               `intention_type` varchar(1000) NULL COMMENT "意图",
                                               `topic` varchar(300) NULL COMMENT "聚合后的观点=>标签叶子结点",
                                               `opinion` varchar(300) NULL COMMENT "原始观点",
                                               `subject` varchar(300) NULL COMMENT "主体【雨刮器】",
                                               `fault_level` varchar(50) NULL COMMENT "故障问题严重性等级",
                                               `description` varchar(200) NULL COMMENT "描述/评价【时灵时不灵】",
                                               `sentiment_score` varchar(10) NULL COMMENT "情感严重程度",
                                               `keywords` varchar(1000) NULL COMMENT "热词",
                                               `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                               `ext_fields` json NULL COMMENT "扩展字段",
                                               `publish_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "发布时间",
                                               `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
                                               `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
                                               `hit_valid_rules` json NULL COMMENT "验证规则id集合",
                                               `hit_rules` json NULL COMMENT "规则id集合",
                                               `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "模型计算数据分析结果表（包含规则命中)"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`, `done`, `new_id`, `id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);




CREATE TABLE `ays_api_reslt_data_analysis_miss` (
                                                    `new_id` varchar(40) NOT NULL COMMENT "主键id",
                                                    `id` varchar(40) NOT NULL COMMENT "主键id",
                                                    `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                                    `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                                    `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                                    `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                                    `input_data_id` varchar(100) NULL COMMENT "原文id",
                                                    `brand_code_name` varchar(1000) NULL COMMENT "品牌名称",
                                                    `car_series_name` varchar(1000) NULL COMMENT "车系名称",
                                                    `opinion` varchar(500) NULL COMMENT "观点",
                                                    `opinion_sentiment` varchar(1000) NULL COMMENT "观点情感",
                                                    `subject` varchar(1000) NULL COMMENT "主体",
                                                    `description` varchar(1000) NULL COMMENT "描述",
                                                    `car_body_label` varchar(1000) NULL COMMENT "整车体系",
                                                    `view_label` varchar(1000) NULL COMMENT "评价维度",
                                                    `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                                    `ext_fields` json NULL COMMENT "扩展字段",
                                                    `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
                                                    `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
                                                    `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "模型计算数据分析结果表（未命中标签的)"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`, `done`, `new_id`, `id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `ays_api_reslt_data_analysis_valid` (
                                                     `new_id` varchar(40) NOT NULL COMMENT "主键id",
                                                     `id` varchar(40) NOT NULL COMMENT "主键id",
                                                     `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                                     `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                                     `old_work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                                     `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                                     `original_id` varchar(40) NOT NULL COMMENT "原文id",
                                                     `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                                     `input_data_id` varchar(100) NULL COMMENT "原文id",
                                                     `sample_data_type` varchar(100) NULL COMMENT "是否是示例数据",
                                                     `original_text_scene` varchar(10000) NULL COMMENT "原文片段",
                                                     `brand_code_name` varchar(1000) NULL COMMENT "品牌名称",
                                                     `car_series_name` varchar(1000) NULL COMMENT "车系名称",
                                                     `b_tag` varchar(2000) NULL COMMENT "业务标签",
                                                     `q_tag` varchar(2000) NULL COMMENT "质量标签",
                                                     `business_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                                     `business_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                                     `business_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                                     `business_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                                     `quality_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                                     `quality_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                                     `quality_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                                     `quality_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                                     `scenario` varchar(500) NULL COMMENT "用车场景",
                                                     `sentiment` varchar(1000) NULL COMMENT "情感",
                                                     `intention_type` varchar(1000) NULL COMMENT "意图",
                                                     `topic` varchar(300) NULL COMMENT "聚合后的观点=>标签叶子结点",
                                                     `opinion` varchar(300) NULL COMMENT "原始观点",
                                                     `subject` varchar(300) NULL COMMENT "主体【雨刮器】",
                                                     `fault_level` varchar(50) NULL COMMENT "故障问题严重性等级",
                                                     `description` varchar(200) NULL COMMENT "描述/评价【时灵时不灵】",
                                                     `sentiment_score` varchar(10) NULL COMMENT "情感严重程度",
                                                     `keywords` varchar(1000) NULL COMMENT "热词",
                                                     `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                                     `ext_fields` json NULL COMMENT "扩展字段",
                                                     `publish_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "发布时间",
                                                     `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
                                                     `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
                                                     `hit_valid_rules` json NULL COMMENT "验证规则id集合",
                                                     `hit_rules` json NULL COMMENT "规则id集合",
                                                     `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "模型计算数据分析结果表（包含规则命中)"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`new_id`, `id`, `work_id`, `old_work_id`, `client_id`, `channel_id`, `content_type`, `original_text_scene`, `create_time`, `done`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);




CREATE TABLE `ays_post_process_data` (
                                         `new_id` varchar(40) NOT NULL COMMENT "主键id",
                                         `id` varchar(40) NOT NULL COMMENT "主键id",
                                         `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                         `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                         `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                         `original_id` varchar(40) NOT NULL COMMENT "原文id",
                                         `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                         `input_data_id` varchar(100) NULL COMMENT "原文id",
                                         `sample_data_type` varchar(100) NULL COMMENT "是否是示例数据",
                                         `original_text_scene` varchar(10000) NULL COMMENT "原文片段",
                                         `brand_code_name` varchar(1000) NULL COMMENT "品牌名称",
                                         `car_series_name` varchar(1000) NULL COMMENT "车系名称",
                                         `b_tag` varchar(2000) NULL COMMENT "业务标签",
                                         `q_tag` varchar(2000) NULL COMMENT "质量标签",
                                         `business_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                         `business_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                         `business_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                         `business_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                         `quality_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                         `quality_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                         `quality_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                         `quality_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                         `scenario` varchar(500) NULL COMMENT "用车场景",
                                         `sentiment` varchar(1000) NULL COMMENT "情感",
                                         `intention_type` varchar(1000) NULL COMMENT "意图",
                                         `topic` varchar(300) NULL COMMENT "聚合后的观点=>标签叶子结点",
                                         `opinion` varchar(300) NULL COMMENT "原始观点",
                                         `subject` varchar(300) NULL COMMENT "主体【雨刮器】",
                                         `fault_level` varchar(50) NULL COMMENT "故障问题严重性等级",
                                         `description` varchar(200) NULL COMMENT "描述/评价【时灵时不灵】",
                                         `sentiment_score` varchar(10) NULL COMMENT "情感严重程度",
                                         `keywords` varchar(1000) NULL COMMENT "热词",
                                         `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                         `ext_fields` json NULL COMMENT "扩展字段",
                                         `publish_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "发布时间",
                                         `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
                                         `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
                                         `abandon` int(11) NULL COMMENT "是否完成计算 是：1，否：0",
                                         `hit_rules` json NULL COMMENT "规则id集合",
                                         `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "模型计算数据分析结果表（包含规则命中)"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`work_id`, `create_time`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `ays_post_process_data_valid` (
                                               `new_id` varchar(40) NOT NULL COMMENT "主键id",
                                               `id` varchar(40) NOT NULL COMMENT "主键id",
                                               `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                               `client_id` varchar(40) NOT NULL COMMENT "客户标识",
                                               `old_work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                               `channel_id` varchar(40) NOT NULL COMMENT "渠道标识",
                                               `original_id` varchar(40) NULL COMMENT "原文id",
                                               `content_type` varchar(10) NULL COMMENT "内容类型：文本：text、 工单：order",
                                               `input_data_id` varchar(100) NULL COMMENT "原文id",
                                               `sample_data_type` varchar(100) NULL COMMENT "是否是示例数据",
                                               `original_text_scene` varchar(10000) NULL COMMENT "原文片段",
                                               `brand_code_name` varchar(1000) NULL COMMENT "品牌名称",
                                               `car_series_name` varchar(1000) NULL COMMENT "车系名称",
                                               `b_tag` varchar(2000) NULL COMMENT "业务标签",
                                               `q_tag` varchar(2000) NULL COMMENT "质量标签",
                                               `business_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                               `business_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                               `business_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                               `business_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                               `quality_label_type_level_first` varchar(200) NULL COMMENT "一级标签",
                                               `quality_label_type_level_second` varchar(200) NULL COMMENT "二级标签",
                                               `quality_label_type_level_three` varchar(200) NULL COMMENT "三级标签",
                                               `quality_label_type_level_four` varchar(200) NULL COMMENT "四级标签/话题",
                                               `scenario` varchar(500) NULL COMMENT "用车场景",
                                               `sentiment` varchar(1000) NULL COMMENT "情感",
                                               `intention_type` varchar(1000) NULL COMMENT "意图",
                                               `topic` varchar(300) NULL COMMENT "聚合后的观点=>标签叶子结点",
                                               `opinion` varchar(300) NULL COMMENT "原始观点",
                                               `subject` varchar(300) NULL COMMENT "主体【雨刮器】",
                                               `fault_level` varchar(50) NULL COMMENT "故障问题严重性等级",
                                               `description` varchar(200) NULL COMMENT "描述/评价【时灵时不灵】",
                                               `sentiment_score` varchar(10) NULL COMMENT "情感严重程度",
                                               `keywords` varchar(1000) NULL COMMENT "热词",
                                               `model_type` int(11) NULL COMMENT "1 智谱AI离线 2智谱AI实时 3聚类大模型",
                                               `ext_fields` json NULL COMMENT "扩展字段",
                                               `publish_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "发布时间",
                                               `create_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "创建时间",
                                               `update_time` datetime NULL DEFAULT CURRENT_TIMESTAMP COMMENT "更新时间",
                                               `hit_valid_rules` json NULL COMMENT "验证规则id集合",
                                               `hit_rules` json NULL COMMENT "规则id集合",
                                               `abandon` int(11) NULL COMMENT "是否遗弃数据 是：1，否：0",
                                               `done` int(11) NULL COMMENT "是否完成计算 是：1，否：0"
) ENGINE=OLAP
    PRIMARY KEY(`new_id`)
COMMENT "模型计算数据分析结果表（包含规则命中)"
DISTRIBUTED BY HASH(`new_id`)
ORDER BY(`new_id`, `id`, `work_id`, `old_work_id`, `client_id`, `channel_id`, `content_type`, `original_text_scene`, `create_time`, `done`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



CREATE TABLE `ays_error_push_data` (
                                       `id` varchar(40) NOT NULL COMMENT "主键",
                                       `table` varchar(100) NOT NULL COMMENT "表名",
                                       `action` varchar(40) NOT NULL COMMENT "操作类型",
                                       `work_id` varchar(40) NOT NULL COMMENT "接收处理标识",
                                       `client_id` varchar(40) NOT NULL COMMENT "消息来源 api,mq,file等",
                                       `data` json NULL COMMENT "",
                                       `create_time` datetime NULL COMMENT "接收时间",
                                       `tid` varchar(55) NULL COMMENT "链路标识"
) ENGINE=OLAP
    PRIMARY KEY(`id`)
COMMENT "数据清洗-入库时异常数据记录表"
DISTRIBUTED BY HASH(`id`)
ORDER BY(`work_id`)
PROPERTIES (
"replication_num" = "1",
"in_memory" = "false",
"enable_persistent_index" = "false",
"replicated_storage" = "true",
"compression" = "LZ4"
);



