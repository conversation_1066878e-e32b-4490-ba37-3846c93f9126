# VOC Cloud 项目概述

## 项目简介
VOC Cloud（Voice of Customer Cloud）是一个基于微服务架构的客户之声分析平台，专注于收集、分析和洞察客户反馈数据，为企业提供全方位的客户体验分析和决策支持。

## 技术栈
- **开发语言**: Java 17
- **框架**: Spring Boot 3.1.5
- **构建工具**: Gradle 8.5
- **微服务治理**: Spring Cloud 2022.0.2
- **服务注册发现**: Nacos 2.x
- **数据库**: MySQL 8.0
- **缓存**: Redis 6.x
- **数据存储**: StarRocks（OLAP数据仓库）
- **消息队列**: Kafka
- **向量数据库**: Milvus
- **对象存储**: MinIO
- **AI模型**: ONNX Runtime
- **监控**: Spring Boot Admin + SkyWalking
- **任务调度**: XXL-Job
- **API文档**: Knife4j (OpenAPI 3)

## 项目特色
- **AI驱动**: 集成机器学习模型进行情感分析、意图识别等智能分析
- **实时处理**: 支持实时数据流处理和分析
- **多租户**: 支持多品牌、多客户的数据隔离
- **可视化**: 丰富的数据可视化和报表功能
- **高可用**: 基于微服务架构，支持横向扩展

## 核心业务领域
- 客户反馈数据收集与清洗
- 多渠道数据整合与标准化
- AI智能分析与洞察
- 实时监控与预警
- 客户体验评估与优化
- 商业智能报表与决策支持

## 系统架构层次
```
┌─────────────────┐
│   应用服务层      │  voc-app-*
├─────────────────┤
│   业务服务层      │  voc-service-*
├─────────────────┤
│   通用组件层      │  voc-service-components
├─────────────────┤
│   基础设施层      │  Database, Cache, MQ
└─────────────────┘
```

## 开发环境
- **本地环境**: http://127.0.0.1:[port]/doc.html
- **开发环境**: http://172.16.80.16:30305/doc.html
- **配置文件**: profiles=local (本地), profiles=dev (开发环境)

## 认证方式
系统采用JWT token认证机制，所有API访问需要在Header中携带：
```
Authorization: Bearer [access_token]
``` 