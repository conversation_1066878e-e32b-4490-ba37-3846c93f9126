# VOC Cloud 业务服务层架构 (voc-service)

业务服务层是系统的核心业务逻辑层，负责具体的业务功能实现，为应用服务层提供可复用的业务能力。

## 核心业务服务

### voc-service-insights
**洞察引擎服务集合**
- **voc-insights-api**: 洞察引擎API定义
- **voc-insights-impl**: 核心洞察实现
- **voc-insights-data-impl**: 数据处理实现
- **voc-insights-model-impl**: 模型处理实现
- **voc-insights-alert-impl**: 告警处理实现
- **voc-insights-common**: 通用组件

**主要功能**:
- 客户体验指标计算
- 情感分析结果聚合
- 实时洞察生成
- 异常检测与预警
- 多维度数据分析

### voc-service-analysis
**数据清洗服务集合**
- **voc-analysis-api**: 分析服务API定义
- **voc-analysis-impl**: 核心分析实现
- **voc-analysis-core-v2**: 分析核心引擎V2
- **voc-analysis-risk**: 风险分析模块

**主要功能**:
- 原始数据清洗和标准化
- 业务规则引擎
- 数据质量检查
- 风险识别和评估
- 数据转换和聚合

**技术特点**:
- 支持StarRocks数据仓库
- 基于规则引擎的数据处理
- 流批一体化处理
- 数据血缘管理

### voc-service-ai-workflow
**AI工作流服务集合**
- **voc-workflow-api**: 工作流API定义
- **voc-workflow-impl**: 工作流实现
- **voc-workflow-onnx**: ONNX模型集成

**主要功能**:
- AI处理流程编排
- 模型推理服务
- 工作流状态管理
- 任务调度和监控

**技术特点**:
- 基于LiteFlow的流程引擎
- ONNX Runtime模型推理
- 支持GPU加速
- 流程可视化设计

### voc-service-model
**模型服务集合**
- **voc-model-api**: 模型服务API
- **voc-model-impl**: 模型实现
- **voc-model-analysis**: 模型分析
- **voc-model-basic**: 基础模型
- **voc-model-tools**: 模型工具

**主要功能**:
- 机器学习模型管理
- 模型训练和评估
- 模型版本控制
- 推理服务
- 模型性能监控

### voc-service-security
**安全服务集合**
- **voc-security-api**: 安全服务API
- **voc-security-server**: 安全服务端
- **voc-security-client**: 安全客户端

**主要功能**:
- 用户认证和授权
- 权限管理
- 安全审计
- 加密服务
- 单点登录(SSO)

### voc-service-data-integration
**数据集成服务**
- **voc-data-integration-api**: 数据集成API
- **voc-mpp-nissan-dndc**: 日产DNDC数据处理

**主要功能**:
- 多数据源接入
- 实时数据同步
- 数据格式转换
- ETL流程管理
- 数据一致性保证

### voc-service-report
**报表服务集合**
- **voc-report-api**: 报表API定义
- **voc-report-cqca-ccag**: 重庆长安汽车报表
- **voc-report-cqca-ccag-api**: 报表API
- **voc-report-cqca-ccag-common**: 报表通用组件
- **voc-report-sso-cqca-ccag**: SSO集成

**主要功能**:
- 动态报表生成
- 数据可视化
- 报表模板管理
- 定时报表任务
- 多格式导出

## 支撑服务

### voc-service-bizlogs
**业务日志服务集合**
- **voc-bizlogs-api**: 业务日志API
- **voc-bizlogs-impl**: 业务日志实现

**主要功能**:
- 业务操作日志记录
- 用户行为追踪
- 系统事件记录
- 日志查询和分析

### voc-service-third
**第三方服务集合**
- **voc-trhird-api**: 第三方服务API
- **voc-trhird-impl**: 第三方服务实现
- **voc-trhird-feishu-impl**: 飞书集成
- **voc-trhird-zhipuai-impl**: 智谱AI集成
- **voc-trhird-template-impl**: 模板服务
- **voc-trhird-util**: 工具类

**主要功能**:
- 消息通知服务
- 第三方API集成
- 模板引擎服务
- AI服务集成
- 工具服务

### voc-service-common
**通用服务组件**
**主要功能**:
- 通用工具类
- 基础数据结构
- 异常处理
- 常量定义
- 通用配置

### voc-service-config
**通用服务属性配置**
**主要功能**:
- 配置管理
- 环境配置隔离
- 动态配置更新
- 配置版本控制

## 数据模型设计

### 业务宽表模型
重庆长安汽车VOC业务宽表包含以下核心字段：
- **基础信息**: 声音ID、数据ID、渠道信息、品牌信息
- **车辆信息**: 车系、车型、VIN码、购买日期等
- **客户信息**: 客户基本信息、地域信息
- **标签体系**: 多级客户体验标签、全旅程标签、销售线索标签等
- **分析结果**: 情感、意图、关键词、热词等

### 查询条件模型
- **时间维度**: 日、周、月、季、年
- **业务维度**: 渠道、品牌、车系、标签等
- **空间维度**: 省份、城市、经销商等

## 技术架构特点

### 微服务架构
- 服务拆分合理，职责清晰
- 服务间松耦合，高内聚
- 支持独立部署和扩展
- 故障隔离和降级

### 数据处理
- 实时流处理和批处理结合
- 多数据源集成能力
- 数据质量保证机制
- 大数据量处理优化

### AI能力
- 模型训练和推理分离
- 支持多种AI模型格式
- 模型版本管理
- A/B测试支持

### 监控运维
- 全链路监控
- 性能指标收集
- 自动化运维
- 故障快速定位 