# VOC Cloud 框架架构文档

本目录包含VOC Cloud项目的完整架构文档，帮助开发者理解系统设计和技术选型。

## 文档导航

### 🏗️ [01-项目概述](./01-项目概述.md)
- 项目简介和技术栈
- 核心业务领域
- 系统架构层次
- 开发环境配置

### 🚀 [02-应用服务层架构](./02-应用服务层架构.md)
- voc-app 模块详解
- REST API服务
- 服务间通信机制
- 配置管理和监控

### ⚙️ [03-业务服务层架构](./03-业务服务层架构.md)
- voc-service 核心业务服务
- 数据处理和分析服务
- AI工作流和模型服务
- 微服务架构特点

### 🔧 [04-通用组件层架构](./04-通用组件层架构.md)
- voc-service-components 基础组件
- 中间件集成方案
- 组件设计原则
- 版本管理策略

### 📊 [05-数据架构与流程](./05-数据架构与流程.md)
- 数据分层架构
- 核心数据模型
- 数据流处理流程
- AI处理和质量管理

### 🎯 [06-系统架构总览](./06-系统架构总览.md)
- 整体架构图
- 技术栈总结
- 部署和安全架构
- 性能和扩展性设计

## 快速开始

### 开发者指南
1. 阅读 [项目概述](./01-项目概述.md) 了解项目背景
2. 查看 [应用服务层架构](./02-应用服务层架构.md) 了解API设计
3. 参考 [业务服务层架构](./03-业务服务层架构.md) 理解业务逻辑
4. 学习 [通用组件层架构](./04-通用组件层架构.md) 掌握基础组件

### 架构师指南
1. 研读 [系统架构总览](./06-系统架构总览.md) 理解整体设计
2. 深入 [数据架构与流程](./05-数据架构与流程.md) 了解数据处理
3. 参考各层架构文档制定技术决策

### 运维指南
1. 查看技术栈和部署架构
2. 了解监控和安全机制
3. 掌握扩展和优化策略

## 技术关键词

- **微服务**: Spring Boot, Spring Cloud, Nacos
- **数据存储**: MySQL, StarRocks, Redis, Milvus
- **消息队列**: Apache Kafka
- **AI/ML**: ONNX Runtime, 情感分析, 意图识别
- **监控**: SkyWalking, Spring Boot Admin
- **容器化**: Docker, Kubernetes

## 相关链接

- [项目主README](../../README.md)
- [需求文档](../requirements/)
- [开发指南](../guide/)
- [研发环境说明](https://futongdf.feishu.cn/wiki/Aa8rwgnJ4iRx4Ik1eW1cZoZfn7e)

## 更新日志

- 2024-01 初始版本，完整架构文档
- 包含6个核心架构文档
- 覆盖应用层、业务层、组件层、数据层
- 提供系统整体架构总览

---

📝 **注意**: 本文档基于当前代码结构分析生成，如有疑问请参考具体代码实现或联系开发团队。 