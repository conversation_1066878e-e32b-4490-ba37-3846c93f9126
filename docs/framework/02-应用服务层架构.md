# VOC Cloud 应用服务层架构 (voc-app)

应用服务层是系统的入口层，负责提供REST API接口，处理用户请求，整合底层业务服务。

## 模块概述

### voc-app-auth
**核心安全服务**
- **功能**: 用户认证、授权管理、权限控制
- **职责**:
  - 用户登录/登出
  - JWT token生成和验证
  - 权限校验和访问控制
  - 多应用集成认证
  - 验证码管理
- **关键特性**:
  - 支持邮箱、手机等多种登录方式
  - 基于角色的权限控制(RBAC)
  - 支持多应用(appId)隔离
  - 集成Spring Security

### voc-app-insights
**洞察引擎服务**
- **功能**: 数据分析与洞察展示
- **职责**:
  - 客户体验指标分析
  - 情感分析结果展示
  - 趋势分析和预测
  - 智能洞察报告生成
  - 实时监控看板
- **关键特性**:
  - 多维度数据分析
  - 可视化图表展示
  - 自定义洞察规则
  - 异常检测和预警

### voc-app-model
**模型计算服务**
- **功能**: AI模型推理与计算
- **职责**:
  - 情感分析模型推理
  - 意图识别模型推理
  - 文本分类和标签预测
  - 模型版本管理
  - 推理结果缓存
- **关键特性**:
  - ONNX模型加载和推理
  - 批量和实时推理
  - 模型性能监控
  - A/B测试支持

### voc-app-analysis
**数据分析服务**
- **功能**: 数据清洗与分析处理
- **职责**:
  - 原始数据清洗和标准化
  - 数据质量检查
  - 业务规则应用
  - 数据转换和聚合
  - 分析任务调度
- **关键特性**:
  - 多数据源接入
  - 实时和批量处理
  - 数据血缘追踪
  - 异常数据处理

### voc-app-ai-workflow
**AI工作流服务**
- **功能**: AI处理流程编排
- **职责**:
  - AI处理流程定义
  - 工作流执行引擎
  - 任务状态管理
  - 流程监控和调试
  - 异常处理和重试
- **关键特性**:
  - 可视化流程设计
  - 动态流程调整
  - 并行任务处理
  - 流程版本控制

### voc-app-data-integration
**数据集成服务**
- **功能**: 外部数据接入与同步
- **职责**:
  - 第三方系统数据接入
  - 数据同步和增量更新
  - 数据格式转换
  - 接入监控和告警
- **关键特性**:
  - 多协议支持(REST、文件、数据库)
  - 增量同步策略
  - 数据一致性保证
  - 错误重试机制

### voc-app-insights-report
**洞察报表服务**
- **功能**: 定制化报表与导出
- **职责**:
  - 报表模板管理
  - 动态报表生成
  - 数据导出功能
  - 报表权限控制
- **关键特性**:
  - 多格式导出(Excel、PDF、图片)
  - 定时报表生成
  - 报表订阅和分发
  - 自定义报表设计

## 项目特定模块

### 重庆长安汽车项目
- **voc-app-data-integration-nissan-dndc**: 日产DNDC数据集成
- **voc-app-report-cqca-ccag**: 重庆长安汽车报表服务

## 服务间通信
- 应用服务层通过OpenFeign调用业务服务层
- 使用Spring Cloud Gateway作为API网关
- 集成Spring Cloud LoadBalancer实现负载均衡
- 通过Nacos进行服务注册与发现

## 配置管理
- 使用Nacos Config进行配置管理
- 支持多环境配置隔离
- 动态配置更新，无需重启服务
- 配置版本控制和回滚

## 监控与日志
- 集成SkyWalking进行链路追踪
- Spring Boot Admin进行应用监控
- 统一日志格式和收集
- 性能指标监控和告警 