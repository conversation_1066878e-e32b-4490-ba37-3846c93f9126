# VOC Cloud 通用组件层架构 (voc-service-components)

通用组件层为整个系统提供基础技术能力和通用组件，支撑上层业务服务的开发和运行。

## 组件概述

### springbootadmin
**Spring Boot Admin 管理组件**
- **功能**: 微服务监控管理中心
- **能力**:
  - 应用健康状态监控
  - 实时日志查看
  - JVM指标监控
  - 配置信息查看
  - 线程堆栈分析
  - HTTP请求追踪
- **特性**:
  - Web界面友好
  - 实时状态更新
  - 告警通知
  - 多环境支持

### voc-cmps-kafka
**Kafka消息队列组件**
- **功能**: 分布式消息流处理
- **能力**:
  - 高吞吐量消息发送
  - 消息分区和副本
  - 消费者组管理
  - 消息持久化存储
  - 流处理支持
- **应用场景**:
  - 数据采集流
  - 事件驱动架构
  - 日志收集
  - 实时数据同步

### voc-cmps-redis
**Redis缓存组件**
- **功能**: 分布式缓存和会话管理
- **能力**:
  - 数据缓存
  - 分布式锁
  - 会话存储
  - 计数器
  - 发布订阅
  - 延时队列
- **应用场景**:
  - 查询结果缓存
  - 用户会话管理
  - 分布式锁控制
  - 实时计数统计

### voc-cmps-milvus
**Milvus向量数据库组件**
- **功能**: 向量相似性搜索
- **能力**:
  - 高维向量存储
  - 相似性搜索
  - 向量索引优化
  - 批量向量操作
  - 多租户支持
- **应用场景**:
  - 文本语义搜索
  - 图像相似度检索
  - 推荐系统
  - 知识图谱

### voc-cmps-onnxruntime
**ONNX Runtime模型推理组件**
- **功能**: 机器学习模型推理服务
- **能力**:
  - ONNX模型加载
  - 高性能推理
  - 多线程推理
  - GPU加速支持
  - 模型预热
  - 批量推理
- **应用场景**:
  - 情感分析
  - 文本分类
  - 实体识别
  - 意图识别

### voc-cmps-xxljob
**XXL-Job分布式任务调度组件**
- **功能**: 分布式定时任务管理
- **能力**:
  - 定时任务调度
  - 任务分片执行
  - 失败重试机制
  - 任务依赖管理
  - 在线日志查看
  - 报警通知
- **应用场景**:
  - 数据同步任务
  - 报表生成任务
  - 数据清理任务
  - 系统维护任务

### voc-cmps-mybatis
**MyBatis数据访问组件**
- **功能**: 数据库操作增强
- **能力**:
  - MyBatis-Plus集成
  - 动态数据源
  - 读写分离
  - 分页插件
  - 数据权限
  - SQL性能监控
- **特性**:
  - 代码生成器
  - 条件构造器
  - 乐观锁支持
  - 逻辑删除

### voc-cmps-elasticsearch
**Elasticsearch搜索引擎组件**
- **功能**: 全文搜索和分析
- **能力**:
  - 全文搜索
  - 聚合分析
  - 实时搜索
  - 地理位置搜索
  - 多语言支持
  - 高亮显示
- **应用场景**:
  - 日志搜索分析
  - 客户反馈搜索
  - 数据分析查询
  - 业务数据检索

### voc-cmps-swagger
**Swagger API文档组件**
- **功能**: API文档生成和管理
- **能力**:
  - 自动API文档生成
  - 在线接口测试
  - 接口参数验证
  - 多版本文档管理
  - 权限控制
- **特性**:
  - Knife4j增强UI
  - OpenAPI 3.0支持
  - 分组管理
  - 导出功能

### voc-cmps-minio
**MinIO对象存储组件**
- **功能**: 分布式对象存储服务
- **能力**:
  - 文件上传下载
  - 分片上传
  - 文件预览
  - 访问权限控制
  - 生命周期管理
  - 版本控制
- **应用场景**:
  - 文档存储
  - 图片存储
  - 模型文件存储
  - 日志文件存储

### voc-cmps-sms
**短信服务组件**
- **功能**: 短信发送和管理
- **能力**:
  - 短信发送
  - 模板管理
  - 发送记录
  - 失败重试
  - 多渠道支持
- **应用场景**:
  - 验证码发送
  - 系统通知
  - 营销短信
  - 告警通知

## 组件设计原则

### 高内聚低耦合
- 每个组件职责单一，功能明确
- 组件间依赖最小化
- 接口定义清晰

### 可配置可扩展
- 支持灵活配置
- 提供扩展点
- 版本向后兼容

### 高可用高性能
- 故障隔离和降级
- 连接池管理
- 性能监控

### 易用性
- 简化配置
- 统一编程模型
- 完善文档

## 组件集成方式

### Spring Boot Starter
- 提供自动配置
- 简化依赖管理
- 统一配置属性

### 配置管理
- 支持多环境配置
- 动态配置更新
- 配置加密

### 监控集成
- 健康检查
- 性能指标收集
- 故障告警

### 日志集成
- 统一日志格式
- 链路追踪
- 日志聚合

## 版本管理
- 语义化版本控制
- 向后兼容保证
- 升级指南提供
- 变更日志维护 