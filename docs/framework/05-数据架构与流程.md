# VOC Cloud 数据架构与流程

## 数据架构概述

VOC Cloud采用现代化的数据架构，支持实时和批处理、多数据源集成、AI智能分析等能力。

### 数据分层架构

```
┌─────────────────────────────────────────────────────┐
│                  应用层 (Application Layer)           │
│           报表服务 | 分析服务 | 洞察服务                │
├─────────────────────────────────────────────────────┤
│                  服务层 (Service Layer)              │
│        数据服务 | 模型服务 | 工作流服务                │
├─────────────────────────────────────────────────────┤
│                 数据仓库层 (Data Warehouse)           │
│                  StarRocks OLAP                     │
├─────────────────────────────────────────────────────┤
│                数据处理层 (Processing Layer)          │
│              实时流处理 | 批处理 | AI处理              │
├─────────────────────────────────────────────────────┤
│                数据接入层 (Ingestion Layer)           │
│            Kafka | API | 文件 | 数据库同步            │
├─────────────────────────────────────────────────────┤
│                 数据源层 (Data Source)               │
│          CRM | 社交媒体 | 客服系统 | 问卷调研          │
└─────────────────────────────────────────────────────┘
```

## 核心数据模型

### 业务宽表模型 (business_wide_table)

#### 基础字段
- **id**: 声音ID (主键)
- **data_id**: 数据唯一标识
- **create_time**: 数据抓取时间
- **data_create_time**: 数据产生时间

#### 渠道维度
- **channel_code**: 渠道编码
- **channel**: 渠道名称
- **label_type**: 数据类型

#### 品牌车型维度
- **brand_code/brand**: 品牌编码/名称
- **vehicle_series_code/vehicle_series**: 车系编码/名称
- **vehicle_model_code/vehicle_model**: 车型编码/名称

#### 客户维度
- **one_id**: 客户唯一标识
- **cust_name**: 客户姓名
- **cust_mobile**: 客户手机号
- **cust_age**: 客户年龄
- **cust_gender**: 客户性别
- **cust_province_code/cust_province**: 客户省份编码/名称
- **cust_city_code/cust_city**: 客户城市编码/名称

#### 车辆信息
- **vehicle_vin**: 车架号
- **vehicle_purchase_date**: 购买日期
- **vehicle_production_date**: 生产日期
- **vehicle_factory_release_date**: 出厂日期

#### 经销商维度
- **dealer_id**: 经销商ID
- **dealer_code**: 经销商编码
- **dealer_name**: 经销商全称
- **dealer_province_code/dealer_province**: 经销商省份
- **dealer_city_code/dealer_city**: 经销商城市

#### AI分析结果
- **sentiment**: 情感分析结果
- **intention**: 意图识别结果
- **hot_word**: 热词提取
- **keywords**: 关键词
- **user_journey**: 用户旅程阶段

#### 标签体系
- **客户体验标签** (CX): cx_tag_first/second/three
- **全旅程标签** (CJ): cj_tag_first/second/three
- **销售线索标签** (SL): sl_tag_first/second/three
- **全媒体标签** (OM): om_tag_first/second/three

#### 时间维度
- **data_create_week**: 周
- **data_create_month**: 月
- **data_create_quarter**: 季度
- **data_create_year**: 年

## 数据流处理架构

### 实时数据流
```
数据源 → Kafka → 流处理引擎 → AI分析 → StarRocks → 实时洞察
```

1. **数据采集**: 通过API、爬虫、文件等方式采集数据
2. **消息队列**: 数据发送到Kafka进行缓冲和分发
3. **实时处理**: 流处理引擎进行清洗、转换、标准化
4. **AI分析**: 调用AI模型进行情感分析、意图识别等
5. **数据存储**: 处理后的数据存储到StarRocks
6. **实时洞察**: 生成实时洞察和预警

### 批处理数据流
```
数据源 → 数据集成 → 批处理引擎 → 数据仓库 → 离线分析
```

1. **数据集成**: 定时从各数据源同步数据
2. **批处理**: 大批量数据清洗、聚合、计算
3. **数据建模**: 构建多维数据模型
4. **报表生成**: 生成各类业务报表

## AI处理流程

### 文本智能分析流程
```
原始文本 → 预处理 → 特征提取 → 模型推理 → 结果后处理 → 存储
```

#### 预处理阶段
- 文本清洗和标准化
- 敏感信息脱敏
- 语言检测
- 文本分句

#### 特征提取
- 分词和词性标注
- 命名实体识别
- 关键词提取
- 向量化表示

#### 模型推理
- **情感分析**: 正面/负面/中性情感识别
- **意图识别**: 用户意图分类
- **主题分类**: 内容主题归类
- **关键信息抽取**: 重要信息提取

#### 结果处理
- 置信度计算
- 结果聚合
- 异常检测
- 质量评估

## 数据质量管理

### 数据质量维度
- **完整性**: 数据字段完整性检查
- **准确性**: 数据格式和范围验证
- **一致性**: 跨系统数据一致性
- **及时性**: 数据更新及时性
- **唯一性**: 数据去重处理

### 质量监控
- 实时质量监控
- 质量报告生成
- 异常数据告警
- 质量趋势分析

## 数据安全与隐私

### 数据脱敏
- 姓名脱敏处理
- 手机号部分隐藏
- 敏感信息加密
- 数据匿名化

### 访问控制
- 基于角色的数据访问
- 数据行级权限控制
- 数据列级权限控制
- 审计日志记录

### 数据备份与恢复
- 定期数据备份
- 增量备份策略
- 快速恢复机制
- 灾难恢复预案

## 性能优化策略

### 存储优化
- 分区表设计
- 索引优化
- 数据压缩
- 冷热数据分离

### 查询优化
- 查询缓存
- 预计算聚合
- 查询重写
- 并行处理

### 计算优化
- 资源池管理
- 任务调度优化
- 批处理优化
- 内存管理

## 数据治理

### 数据标准
- 数据字典管理
- 编码标准制定
- 命名规范
- 数据模型标准

### 数据血缘
- 数据来源追踪
- 数据流向分析
- 影响分析
- 变更影响评估

### 元数据管理
- 技术元数据
- 业务元数据
- 操作元数据
- 数据资产目录 