package com.voc.service.insights.report.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.expression.ExpressionUtil;
import com.alicp.jetcache.Cache;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.CreateCache;
import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Options;
import com.voc.service.common.util.ServiceContextHolder;
import com.voc.service.insights.engine.api.constants.InsightsConstants;
import com.voc.service.insights.report.api.api.IInsReportSystemConfigService;
import com.voc.service.insights.report.api.api.IInsReportThresholdService;
import com.voc.service.insights.report.api.enums.LabelTypeEnum;
import com.voc.service.insights.report.api.enums.PeriodEnum;
import com.voc.service.insights.report.api.model.CommonFilterModel;
import com.voc.service.insights.report.api.model.InsReportSystemInfoModel;
import com.voc.service.insights.report.api.model.indicators.IndicatorsEmojiParamModel;
import com.voc.service.insights.report.api.model.indicators.IndicatorsNSRParamModel;
import com.voc.service.insights.report.api.model.indicators.IndicatorsNSRResultModel;
import com.voc.service.insights.report.api.vo.IndicatorsEmojiParamVo;
import com.voc.service.insights.report.api.vo.InsReportSystemInfoVo;
import com.voc.service.insights.report.config.ReportConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.util.ConcurrentReferenceHashMap;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * @Title: ReportBaseService
 * @Package: com.voc.service.insights.report.impl
 * @Description:
 * @Author: cuick
 * @Date: 2024/9/13 16:02
 * @Version:1.0
 */
@Slf4j
public abstract class ReportBaseService {
    @Autowired
    ReportConfig config;
    @Autowired
    IInsReportSystemConfigService reportSystemConfigService;
    @CreateCache(area = "VDP", name = ":", expire = 60 * 60, cacheType = CacheType.REMOTE)
    private Cache<String, String> settingsCache;
    private static final String SETTINGS_KEY = "{}:settings:{}";
    @Autowired
    IInsReportThresholdService reportThresholdService;

    private String getSettingsKey(Object... params) {
        return StrUtil.format(SETTINGS_KEY, ServiceContextHolder.getSystemId(), params);
    }

    static {
        AviatorEvaluator.getInstance().setOption(Options.ALWAYS_PARSE_FLOATING_POINT_NUMBER_INTO_DECIMAL, true);
// -- 2. 解析整数为 Decimal 类型
        AviatorEvaluator.getInstance().setOption(Options.ALWAYS_PARSE_INTEGRAL_NUMBER_INTO_DECIMAL, true);
    }

    public void permissionsFilter(CommonFilterModel filterModel) {
        boolean admin = ServiceContextHolder.isAdmin();

        //品牌权限
        this.filterBrandData(filterModel, admin);
        //数据服务类型
        this.filterTagLabelCategory(filterModel, admin);
        //渠道权限
        this.filterChannelIdData(filterModel, admin);
        //车系权限
        this.filterCarSeriesData(filterModel, admin);
        //区域
        this.filterRegionData(filterModel, admin);
        //
        //标签分类条件判断，若选择了标签分类，则需要最先执行，然后再执行其他权限过滤
        this.filterTagSearchLevelCategory(filterModel, admin);
        //一级标签
        this.filterLabelTypeLevelFirstListData(filterModel, admin);
        //二级标签
        this.filterLabelTypeLevelSecondListData(filterModel, admin);


        //设置禁用的标签
        this.filterDisabledTags(filterModel, admin);

        this.setDefaultValues(filterModel);
    }


    /**
     * 数据服务类型
     *
     * @param filterModel
     */
    private void filterTagLabelCategory(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "标签类型");
        log.debug("user：{}", ServiceContextHolder.getUser());

        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }
        if (StrUtil.isNotEmpty(filterModel.getTagType())) {
            filterModel.setLabelTypeList(Set.of(filterModel.getTagType()));
        }

        Set<String> tagTypeList = Collections.synchronizedSet(new HashSet<>());
        final Map<String, Map<String, List<String>>> brandMap = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap.entrySet().stream().filter(e -> e.getKey().equals(filterModel.getBrandCodeList().stream().findAny().get())).forEach(e -> {
            Map<String, List<String>> value = e.getValue();
            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_APP_TAG_LIB_KEY)).forEach(k -> {
                tagTypeList.addAll(k.getValue());
            });
        });

        //当前用户渠道数据权限范围
//        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_CAR_SERIES_KEY_NAME));
        Optional<Object> perms = Optional.ofNullable(tagTypeList);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Set<String> values = (Set<String>) perms.get();
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("标签类型权限数据：{}", values);
                        if (CollUtil.isEmpty(filterModel.getLabelTypeList())) {
                            filterModel.setLabelTypeList(values);
                            return;
                        }

                        //重新赋值交集数据
                        filterModel.setLabelTypeList(new HashSet<>(CollUtil.intersection(filterModel.getLabelTypeList(), values)));
                        log.debug("标签类型权限 {}", filterModel.getLabelTypeList());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeList(Set.of("empty"));
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效标签类型数据 user:{}", ServiceContextHolder.getUser());
        } finally {
            //转换code
            filterModel.setLabelTypeList(
                    filterModel.getLabelTypeList().stream().map(e -> {
                        if (LabelTypeEnum.SERVICE.getCode().equals(e)) {
                            return LabelTypeEnum.SERVICE.getDbCode();
                        } else if (LabelTypeEnum.PROD.getCode().equals(e)) {
                            return LabelTypeEnum.PROD.getDbCode();
                        } else if (LabelTypeEnum.QY.getCode().equals(e)) {
                            return LabelTypeEnum.QY.getDbCode();
                        }
                        return null;
                    }).filter(StrUtil::isNotEmpty).collect(Collectors.toSet())
            );
            log.debug("标签类型权限转换后：{}", filterModel.getLabelTypeList());
        }
    }

    private void filterLabelTypeLevelSecondListData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "二级标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }
        Set<String> tabList = Collections.synchronizedSet(new HashSet<>());
        final Map<String, Map<String, List<String>>> brandMap1 = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap1.entrySet().stream().filter(e -> e.getKey().equals(filterModel.getBrandCodeList().stream().findAny().get())).forEach(e -> {
            Map<String, List<String>> value = e.getValue();
            Set<String> values = new HashSet<>();
            if (CollUtil.isNotEmpty(filterModel.getLabelTypeList()) && filterModel.getLabelTypeList().contains(LabelTypeEnum.PROD.getDbCode())) {
                value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_BIZ_TAG_LEVE_SECOND_LIB_KEY)).forEach(k -> {
                    values.addAll(k.getValue());
                });
            }
            if (CollUtil.isNotEmpty(filterModel.getLabelTypeList()) && filterModel.getLabelTypeList().contains(LabelTypeEnum.QY.getDbCode())) {
                value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_QY_TAG_LEVE_SECOND_LIB_KEY)).forEach(k -> {
                    values.addAll(k.getValue());
                });
            }
            if (CollUtil.isNotEmpty(filterModel.getLabelTypeList()) && filterModel.getLabelTypeList().contains(LabelTypeEnum.SERVICE.getDbCode())) {
                value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_SERVICE_TAG_LEVE_SECOND_LIB_KEY)).forEach(k -> {
                    values.addAll(k.getValue());
                });
            }
            tabList.addAll(values);
        });

        //当前用户渠道数据权限范围
//        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_CAR_SERIES_KEY_NAME));
        Optional<Object> perms = Optional.ofNullable(tabList);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Set<String> values = (Set<String>) perms.get();
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("二级标签权限数据：{}", values);
                        if (CollUtil.isEmpty(filterModel.getLabelTypeLevelSecondList())) {
                            filterModel.setLabelTypeLevelSecondList(values);
                            return;
                        }
                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        if (CollUtil.isEmpty(values)) {      //  默认加载全部
                            throw new Exception("[二级标签]前端传入的数据异常，".concat(String.valueOf(filterModel.getLabelTypeLevelSecondList())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setLabelTypeLevelSecondList(new HashSet<>(CollUtil.intersection(values, filterModel.getLabelTypeLevelSecondList())));
                        }
                        //重新赋值交集数据
                        log.debug("二级标签权限 {}", filterModel.getLabelTypeLevelSecondList());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelSecondList(Set.of("empty"));
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效二级标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    private void filterLabelTypeLevelFirstListData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "一级标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }
        Set<String> tabList = Collections.synchronizedSet(new HashSet<>());
        final Map<String, Map<String, List<String>>> brandMap1 = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap1.entrySet().stream().filter(e -> e.getKey().equals(filterModel.getBrandCodeList().stream().findAny().get())).forEach(e -> {
            Map<String, List<String>> value = e.getValue();
            Set<String> values = new HashSet<>();
            if (CollUtil.isNotEmpty(filterModel.getLabelTypeList()) && filterModel.getLabelTypeList().contains(LabelTypeEnum.PROD.getDbCode())) {
                value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_BIZ_TAG_LEVE_FIRST_LIB_KEY)).forEach(k -> {
                    values.addAll(k.getValue());
                });
            }
            if (CollUtil.isNotEmpty(filterModel.getLabelTypeList()) && filterModel.getLabelTypeList().contains(LabelTypeEnum.QY.getDbCode())) {
                value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_QY_TAG_LEVE_FIRST_LIB_KEY)).forEach(k -> {
                    values.addAll(k.getValue());
                });
            }
            if (CollUtil.isNotEmpty(filterModel.getLabelTypeList()) && filterModel.getLabelTypeList().contains(LabelTypeEnum.SERVICE.getDbCode())) {
                value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_SERVICE_TAG_LEVE_FIRST_LIB_KEY)).forEach(k -> {
                    values.addAll(k.getValue());
                });
            }
            tabList.addAll(values);
        });

        //当前用户渠道数据权限范围
//        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_CAR_SERIES_KEY_NAME));
        Optional<Object> perms = Optional.ofNullable(tabList);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Set<String> values = (Set<String>) perms.get();
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("一级标签权限数据：{}", values);
                        if (CollUtil.isEmpty(filterModel.getLabelTypeLevelFirstList())) {
                            filterModel.setLabelTypeLevelFirstList(values);
                            return;
                        }
                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        if (CollUtil.isEmpty(values)) {      //  默认加载全部
                            throw new Exception("[一级标签]前端传入的数据异常，".concat(String.valueOf(filterModel.getLabelTypeLevelFirstList())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setLabelTypeLevelFirstList(new HashSet<>(CollUtil.intersection(values, filterModel.getLabelTypeLevelFirstList())));
                        }
                        //重新赋值交集数据
                        log.debug("一级标签权限 {}", filterModel.getLabelTypeLevelFirstList());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFirstList(Set.of("empty"));
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效一级标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    /**
     * 设置禁用的标签
     *
     * @param filterModel
     */
    private void filterDisabledTags(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "四级标签禁用");
        log.debug("user：{}", ServiceContextHolder.getUser());

        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }


        Object tabList = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_ALL_DISABLE_FOUR_TAG_LIB_LIB_MAP);

        Optional<Object> perms = Optional.ofNullable(tabList);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, String> map = (Map<String, String>) perms.get();

                    final Set<String> values = map.keySet();
                    log.info("获取全部禁用标签:{}", values);
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("四级标签禁用权限数据：{}", values);
                        if (CollUtil.isNotEmpty(filterModel.getLabelTypeLevelFourList())) {
                            Set<String> filterList = new ConcurrentHashSet<>(filterModel.getLabelTypeLevelFourList());
                            filterList.removeAll(values);
                            log.info("四级标签禁用权限过滤 {}", values);
                            filterModel.setLabelTypeLevelFourList(filterList);
                            return;
                        }
                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        if (CollUtil.isEmpty(values)) {      //  默认加载全部
                            throw new Exception("[四级标签禁用]前端传入的数据异常，".concat(String.valueOf(filterModel.getLabelTypeLevelFourDisableList())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setLabelTypeLevelFourDisableList(values);
                        }
                        //重新赋值交集数据
                        log.debug("四级标签禁用权限 {}", filterModel.getLabelTypeLevelFourDisableList());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
//                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFourDisableList(Set.of("empty"));
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效四级标签禁用数据 user:{}", ServiceContextHolder.getUser());
        }
    }


    private void setDefaultValues(CommonFilterModel filterModel) {
        filterModel.setClientId(ServiceContextHolder.getClientId());
        //userId
//        UserModel model = ServiceContextHolder.getUser();
        final String userId = ServiceContextHolder.getUserId();
        filterModel.setUserId(ServiceContextHolder.isAdmin() ? "admin" : ServiceContextHolder.getUserId());
    }


    public String getSystemDatePeriodSetting(String clientId) {
        //此处实现读取系统配置项
        return settingsCache.computeIfAbsent(this.getSettingsKey(clientId), (key) -> {
            InsReportSystemInfoVo systemConfig = reportSystemConfigService.findSystemConfig(InsReportSystemInfoModel.builder().clientId(clientId).build());
            if (Set.of(PeriodEnum.D.getCode(), PeriodEnum.M.getCode(), PeriodEnum.W.getCode()).contains(systemConfig.getDefaultPeriod())) {
                log.info("使用系统默认值：{}", systemConfig.getDefaultPeriod());
                return systemConfig.getDefaultPeriod();
            }
            return PeriodEnum.M.getCode();
        });
    }

    public void filterTagSearchLevelCategory(CommonFilterModel filterModel, boolean admin) {


        log.debug("分类条件判断");
        log.debug("user：{}", ServiceContextHolder.getUser());

        log.debug("进行权限过滤 {}", "车系");
        log.debug("user：{}", ServiceContextHolder.getUser());

        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }
        Map<String, Set<String>> map = new ConcurrentReferenceHashMap<>();
        try {


            if (CollUtil.isNotEmpty(filterModel.getLabelTypeLevelFirstList())
                    || CollUtil.isNotEmpty(filterModel.getLabelTypeLevelSecondList())
                    || CollUtil.isNotEmpty(filterModel.getLabelTypeLevelThreeList())
            ) {
                if(CollUtil.isNotEmpty(filterModel.getLabelTypeLevelFirstList())){
                    filterModel.setSearchLabelLevel("2");
                }else  if(CollUtil.isNotEmpty(filterModel.getLabelTypeLevelSecondList())){
                    filterModel.setSearchLabelLevel("3");
                }else{
                    filterModel.setSearchLabelLevel("1");
                }
            } else if (CollUtil.isNotEmpty(filterModel.getTagLabelList())) {
                log.debug("标签分类数据为空：{}", filterModel.getTagLabelList());

                /**
                 * [
                 *         [
                 *             "DNDC1001",
                 *             "DNDC1001001"
                 *         ],
                 *         [
                 *             "DNDC1001",
                 *             "DNDC1001006"
                 *         ],
                 *         [
                 *             "DNDC1001"
                 *         ],
                 *         [
                 *             "DNDC1004"
                 *         ]
                 *     ]
                 *     转换
                 *     DNDC1001: [DNDC1001001,DNDC1001006]
                 *     DNDC1004: []
                 *
                 */

                final Set<String> inputLabelTypeLevelSecondList = filterModel.getLabelTypeLevelSecondList();
                final Set<String> inputLabelTypeLevelFirstList = filterModel.getLabelTypeLevelFirstList();
                filterModel.getTagLabelList().stream().forEach(e -> {
                    final String f1 = e.get(0);
                    if (map.containsKey(f1)) {
                        if (e.size() > 1) {
                            map.get(f1).add(e.get(1));
                        }
                    } else {
                        if (e.size() > 1) {
                            map.put(f1, new HashSet<>(Set.of(e.get(1))));
                        } else {
                            map.put(f1, new HashSet<>());
                        }
                    }
                });


                /**
                 * DNDC1001: [DNDC1001001,DNDC1001006]
                 * DNDC1004: []
                 * 转成
                 *  DNDC1001001,DNDC1001006 跟权限的范围做交集
                 *  []  会把权限范围数据补充进来
                 *  转成
                 *
                 *  二级比钱集合
                 *  DNDC1001001,DNDC1001006 + DNDC1001007,DNDC1001008
                 */
                //获取下级标签
                filterModel.setLabelTypeLevelSecondList(new HashSet<>());
                filterModel.setLabelTypeLevelFirstList(new HashSet<>());

                map.keySet().stream().forEach(e -> {
                    //单独选择的一级
                    if (CollUtil.isEmpty(map.get(e))) {
                        filterModel.getLabelTypeLevelSecondList().addAll(this.getSubList(e));
                    } else {
                        final Set<String> vals = map.get(e).stream()
                                .map(k -> {
                                    Set<String> rs = this.getSubList(k);
                                    if (CollUtil.isNotEmpty(rs)) {
                                        return rs.stream().findFirst().get();
                                    }
                                    return null;
                                })
                                .collect(Collectors.toSet());
                        filterModel.getLabelTypeLevelSecondList().addAll(vals);
                    }
                });
                if (CollUtil.isNotEmpty(inputLabelTypeLevelSecondList) && CollUtil.isNotEmpty(filterModel.getLabelTypeLevelSecondList())) {
                    filterModel.setLabelTypeLevelSecondList(new HashSet<>(CollUtil.intersection(inputLabelTypeLevelSecondList, filterModel.getLabelTypeLevelSecondList())));
                } else if (CollUtil.isEmpty(filterModel.getLabelTypeLevelSecondList())) {
                    filterModel.setLabelTypeLevelSecondList(inputLabelTypeLevelSecondList);
                }


                if (CollUtil.isNotEmpty(map)) {
                    map.keySet().stream().forEach(key -> {
                        final String title = this.getSubList(filterModel.getBrandCodeList().stream().findFirst().get(), key);
                        log.debug("一级标签：{}", title);
                        if (StrUtil.isNotEmpty(title)) {
                            filterModel.getLabelTypeLevelFirstList().add(title);
                        }
                    });
                }
                if (CollUtil.isEmpty(map.keySet())) {
                    //默认查询一级
                    filterModel.setSearchLabelLevel("1");
                } else if (map.keySet().stream().filter(key -> CollUtil.isNotEmpty(map.get(key))).count() == 0) {
                    filterModel.setSearchLabelLevel("2");
                } else if (map.keySet().stream().filter(key -> CollUtil.isNotEmpty(map.get(key))).count() > 0) {
                    filterModel.setSearchLabelLevel("3");
                } else {
                    //默认查询一级
                    filterModel.setSearchLabelLevel("1");
                }
                log.info("一级标签：{} ， {}", map.keySet(), map);
            }else{
                //默认值-查询一级
                filterModel.setSearchLabelLevel("1");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {

            log.info("标签查询级别：{}", filterModel.getSearchLabelLevel());
        }
    }

    /**
     * @return
     */
    private String getSubList(String brand, String code) {

        Set<String> values = new ConcurrentHashSet<>();
        final Map<String, Map<String, List<String>>> brandMap1 = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap1.entrySet().stream().filter(e -> e.getKey().equals(brand)).forEach(e -> {
            Map<String, List<String>> value = e.getValue();

            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_BIZ_TAG_LEVE_FIRST_LIB_MAP)).forEach(k -> {
                values.addAll(k.getValue());
            });
            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_QY_TAG_LEVE_FIRST_LIB_MAP)).forEach(k -> {
                values.addAll(k.getValue());
            });
            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_SERVICE_TAG_LEVE_FIRST_LIB_MAP)).forEach(k -> {
                values.addAll(k.getValue());
            });
        });
        log.debug("一级标签：{}", values);

        Map<String, String> map = new ConcurrentHashMap<>();
        values.stream().forEach(v -> {
            if (StrUtil.contains(v, ":")) {
                List<String> arr = StrUtil.split(v, ":");
                if (arr.size() == 2) {
                    final String key = arr.get(0);
                    final String val = arr.get(1);
                    map.put(key, val);
                }
            }
        });

        return map.get(code);
    }

    private Set<String> getSubList(String parentCode) {
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_TAG_ALL_LIB_KEY));
        if (perms.isPresent()) {
            Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
            Map<String, List<String>> value = modelList.entrySet().stream().findFirst().orElseGet(null).getValue();
            List<String> rs = value.get(parentCode);
            if (CollUtil.isNotEmpty(rs)) {
                return new HashSet<>(value.get(parentCode));
            }
        }
        return Set.of();
    }


    /**
     * 渠道
     *
     * @param filterModel
     * @param admin
     */
    public void filterChannelIdData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "渠道");
        log.debug("user：{}", ServiceContextHolder.getUser());
        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }

        Set<String> channelIds = Collections.synchronizedSet(new HashSet<>());
        final Map<String, Map<String, List<String>>> brandMap = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap.entrySet().stream().filter(e -> e.getKey().equals(filterModel.getBrandCodeList().stream().findAny().get())).forEach(e -> {
            Map<String, List<String>> value = e.getValue();
            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_BIZ_CHANEL_DATA_KEY)).forEach(k -> {
                channelIds.addAll(k.getValue());
            });
        });

        //当前用户渠道数据权限范围
//        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BIZ_CHANEL_DATA_KEY));
        Optional<Object> perms = Optional.ofNullable(channelIds);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Set<String> values = (Set<String>) perms.get();
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("渠道权限数据：{}", values);
                        if (CollUtil.isEmpty(filterModel.getChannelIds())) {
                            filterModel.setChannelIds(values);
                            filterModel.setRiskChannelIds(StringUtils.join(values, ","));
                            return;
                        }
                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        final Set<String> list = values.stream().filter(filterModel.getChannelIds()::contains).collect(Collectors.toSet());
                        if (CollUtil.isEmpty(list)) {      //  默认加载全部
                            throw new Exception("[渠道]前端传入的数据异常，".concat(String.valueOf(filterModel.getChannelIds())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setChannelIds(new HashSet<>(CollUtil.intersection(values, list)));
                            filterModel.setRiskChannelIds(StringUtils.join(filterModel.getChannelIds(), ","));
                        }
                        //重新赋值交集数据
                        log.debug("渠道权限 {}", filterModel.getChannelIds());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setChannelIds(Set.of("empty"));
            filterModel.setRiskChannelIds("empty");
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效品牌数据 user:{}", ServiceContextHolder.getUser());
        }
    }


    /**
     * @param filterModel
     * @param admin
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/11 上午10:29
     * @描述 品牌权限
     **/
    public void filterBrandData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "品牌");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        log.debug("品牌权限数据：{}", modelList);
                        //品牌不允许空值
                        if (CollUtil.isEmpty(filterModel.getBrandCodeList())) {
                            throw new Exception("品牌不允许空值");
                        }

                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        final Set<String> brandCode = modelList.keySet();
                        final Set<String> list = brandCode.stream().filter(filterModel.getBrandCodeList()::contains).collect(Collectors.toSet());
                        if (CollUtil.isEmpty(list)) {      //  默认加载全部
                            throw new Exception("[品牌]前端传入的数据异常，".concat(String.valueOf(filterModel.getBrandCodeList())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setBrandCodeList(new HashSet<>(CollUtil.intersection(brandCode, list)));
                        }
                        //重新赋值交集数据
                        log.debug("品牌权限 {}", filterModel.getChannelIds());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setBrandCodeList(Set.of("empty"));
            log.error("当前用户未配置有效品牌数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    /**
     * @param filterModel
     * @param admin
     * @return void
     * @创建者/修改者 fanrong
     * @创建/更新日期 2024/11/11 上午10:29
     * @描述 车系权限
     **/
    public void filterCarSeriesData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "车系");
        log.debug("user：{}", ServiceContextHolder.getUser());

        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }
        Set<String> carSeriesList = Collections.synchronizedSet(new HashSet<>());
        final Map<String, Map<String, List<String>>> brandMap = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap.entrySet().stream().filter(e -> e.getKey().equals(filterModel.getBrandCodeList().stream().findAny().get())).forEach(e -> {
            Map<String, List<String>> value = e.getValue();
            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_CAR_SERIES_KEY_NAME)).forEach(k -> {
                carSeriesList.addAll(k.getValue());
            });
        });

        //当前用户渠道数据权限范围
//        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_CAR_SERIES_KEY_NAME));
        Optional<Object> perms = Optional.ofNullable(carSeriesList);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Set<String> values = (Set<String>) perms.get();
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("渠道权限数据：{}", values);
                        if (CollUtil.isEmpty(filterModel.getCarSeriesList())) {
                            filterModel.setCarSeriesList(values);
                            filterModel.setRiskCarSeriesList(StringUtils.join(values, ","));
                            return;
                        }
                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        final Set<String> list = values.stream().filter(filterModel.getCarSeriesList()::contains).collect(Collectors.toSet());
                        if (CollUtil.isEmpty(list)) {      //  默认加载全部
                            throw new Exception("[车系]前端传入的数据异常，".concat(String.valueOf(filterModel.getCarSeriesList())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setCarSeriesList(new HashSet<>(CollUtil.intersection(values, list)));
                            filterModel.setRiskCarSeriesList(StringUtils.join(filterModel.getCarSeriesList(), ","));
                        }
                        //重新赋值交集数据
                        log.debug("渠道权限 {}", filterModel.getCarSeriesList());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setCarSeriesList(Set.of("empty"));
            filterModel.setRiskCarSeriesList("empty");
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效品牌数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterAllTagLevelFirstData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "全部一级标签");
        log.debug("user：{}", ServiceContextHolder.getUser());
        List<String> allTagLevelFirstList = new ArrayList<>();
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                //当前用户渠道数据权限范围
                Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
                Map<String, List<String>> value = Map.of();
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                    }
                }
                if (ObjectUtils.isNotEmpty(value)) {
                    Set<String> labelTypeList = filterModel.getLabelTypeList();
                    Map<String, List<String>> finalValue = value;
                    labelTypeList.stream().forEach(e -> {
                        if (LabelTypeEnum.PROD.getDbCode().equals(e)) {
                            //产品一级标签
                            if (finalValue.containsKey(InsightsConstants.PERMS_BIZ_TAG_LEVE_FIRST_LIB_KEY)) {
                                List<String> list = finalValue.get(InsightsConstants.PERMS_BIZ_TAG_LEVE_FIRST_LIB_KEY);
                                allTagLevelFirstList.addAll(list);
                            }
                        } else if (LabelTypeEnum.SERVICE.getDbCode().equals(e)) {
                            if (finalValue.containsKey(InsightsConstants.PERMS_SERVICE_TAG_LEVE_FIRST_LIB_KEY)) {
                                List<String> list = finalValue.get(InsightsConstants.PERMS_SERVICE_TAG_LEVE_FIRST_LIB_KEY);
                                allTagLevelFirstList.addAll(list);
                            }
                        } else if (LabelTypeEnum.QY.getDbCode().equals(e)) {
                            if (finalValue.containsKey(InsightsConstants.PERMS_QY_TAG_LEVE_FIRST_LIB_KEY)) {
                                List<String> list = finalValue.get(InsightsConstants.PERMS_QY_TAG_LEVE_FIRST_LIB_KEY);
                                allTagLevelFirstList.addAll(list);
                            }
                        }
                    });
                } else {
                    throw new Exception("is empty");
                }

                if (ObjectUtils.isNotEmpty(allTagLevelFirstList)) {
                    final Set<String> input = filterModel.getLabelTypeLevelFirstList();
                    final Set<String> list = allTagLevelFirstList.stream().filter(input::contains).collect(Collectors.toSet());
                    if (CollUtil.isEmpty(input)) {      //  默认加载全部
                        Set<String> collect = allTagLevelFirstList.stream().collect(Collectors.toSet());
                        filterModel.setLabelTypeLevelFirstList(collect);
                    } else {                          //过滤
                        filterModel.setLabelTypeLevelFirstList(new HashSet<>(CollUtil.intersection(input, list)));
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFirstList(Set.of("empty"));
            filterModel.setLabelTypeLevelSecondList(Set.of("empty"));
            log.error("当前用户未配置有效标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterAllTagLevelSecondData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "全部二级标签");
        log.debug("user：{}", ServiceContextHolder.getUser());
        List<String> allTagLevelSecondList = new ArrayList<>();
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                //当前用户渠道数据权限范围
                Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
                Map<String, List<String>> value = Map.of();
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                    }
                }
                if (ObjectUtils.isNotEmpty(value)) {
                    Set<String> labelTypeList = filterModel.getLabelTypeList();
                    Map<String, List<String>> finalValue = value;
                    labelTypeList.stream().forEach(e -> {
                        if (LabelTypeEnum.PROD.getDbCode().equals(e)) {
                            //产品一级标签
                            if (finalValue.containsKey(InsightsConstants.PERMS_BIZ_TAG_LEVE_SECOND_LIB_KEY)) {
                                List<String> list = finalValue.get(InsightsConstants.PERMS_BIZ_TAG_LEVE_SECOND_LIB_KEY);
                                allTagLevelSecondList.addAll(list);
                            }
                        } else if (LabelTypeEnum.SERVICE.getDbCode().equals(e)) {
                            if (finalValue.containsKey(InsightsConstants.PERMS_SERVICE_TAG_LEVE_SECOND_LIB_KEY)) {
                                List<String> list = finalValue.get(InsightsConstants.PERMS_SERVICE_TAG_LEVE_SECOND_LIB_KEY);
                                allTagLevelSecondList.addAll(list);
                            }
                        } else if (LabelTypeEnum.QY.getDbCode().equals(e)) {
                            if (finalValue.containsKey(InsightsConstants.PERMS_QY_TAG_LEVE_SECOND_LIB_KEY)) {
                                List<String> list = finalValue.get(InsightsConstants.PERMS_QY_TAG_LEVE_SECOND_LIB_KEY);
                                allTagLevelSecondList.addAll(list);
                            }
                        }
                    });
                } else {
                    throw new Exception("is empty");
                }

                if (ObjectUtils.isNotEmpty(allTagLevelSecondList)) {
                    final Set<String> input = filterModel.getLabelTypeLevelFirstList();
                    final Set<String> list = allTagLevelSecondList.stream().filter(input::contains).collect(Collectors.toSet());
                    if (CollUtil.isEmpty(input)) {      //  默认加载全部
                        Set<String> collect = allTagLevelSecondList.stream().collect(Collectors.toSet());
                        filterModel.setLabelTypeLevelSecondList(collect);
                    } else {                          //过滤
                        filterModel.setLabelTypeLevelSecondList(new HashSet<>(CollUtil.intersection(input, list)));
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFirstList(Set.of("empty"));
            filterModel.setLabelTypeLevelSecondList(Set.of("empty"));
            log.error("当前用户未配置有效标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }


    public void filterBusinessTagLevelFirstData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "业务标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        Map<String, List<String>> value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                        if (ObjectUtils.isNotEmpty(value)) {
                            final Set<String> input = filterModel.getLabelTypeLevelFirstList();
                            if (!value.containsKey(InsightsConstants.PERMS_BIZ_TAG_LEVE_FIRST_LIB_KEY)) {
                                throw new Exception("is empty");
                            }
                            final List<String> bizLevelFirstList = value.get(InsightsConstants.PERMS_BIZ_TAG_LEVE_FIRST_LIB_KEY);
                            final Set<String> list = bizLevelFirstList.stream().filter(input::contains).collect(Collectors.toSet());
                            if (CollUtil.isEmpty(input)) {      //  默认加载全部
                                Set<String> collect = bizLevelFirstList.stream().collect(Collectors.toSet());
                                filterModel.setLabelTypeLevelFirstList(collect);
                                filterModel.setRiskLabelTypeLevelFirstList(StringUtils.join(collect, ","));
                            } else {                          //过滤
                                filterModel.setLabelTypeLevelFirstList(new HashSet<>(CollUtil.intersection(input, list)));
                                filterModel.setRiskLabelTypeLevelFirstList(StringUtils.join(filterModel.getRiskLabelTypeLevelFirstList(), ","));
                            }
                            //重新赋值交集数据
                            log.debug("业务标签权限 {}", filterModel.getChannelIds());
                        } else {
                            throw new Exception("is empty");
                        }
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFirstList(Set.of("empty"));
            filterModel.setRiskLabelTypeLevelFirstList("empty");
            log.error("当前用户未配置有效业务标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterBusinessTagLevelSecondData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "业务标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        Map<String, List<String>> value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                        if (ObjectUtils.isNotEmpty(value)) {
                            final Set<String> input = filterModel.getLabelTypeLevelSecondList();
                            if (!value.containsKey(InsightsConstants.PERMS_BIZ_TAG_LEVE_SECOND_LIB_KEY)) {
                                throw new Exception("is empty");
                            }
                            final List<String> bizLevelSecondList = value.get(InsightsConstants.PERMS_BIZ_TAG_LEVE_SECOND_LIB_KEY);
                            final Set<String> list = bizLevelSecondList.stream().filter(input::contains).collect(Collectors.toSet());
                            if (CollUtil.isEmpty(input)) {      //  默认加载全部
                                Set<String> collect = bizLevelSecondList.stream().collect(Collectors.toSet());
                                filterModel.setLabelTypeLevelSecondList(collect);
                                filterModel.setRiskLabelTypeLevelSecondList(StringUtils.join(collect, ","));
                            } else {                          //过滤
                                filterModel.setLabelTypeLevelSecondList(new HashSet<>(CollUtil.intersection(input, bizLevelSecondList)));
                                filterModel.setRiskLabelTypeLevelSecondList(StringUtils.join(filterModel.getLabelTypeLevelSecondList(), ","));
                            }
                            //重新赋值交集数据
                            log.debug("业务标签权限 {}", filterModel.getChannelIds());
                        } else {
                            throw new Exception("is empty");
                        }
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelSecondList(Set.of("empty"));
            filterModel.setRiskLabelTypeLevelSecondList("empty");
            log.error("当前用户未配置有效业务标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterQYTagLevelFirstData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "业务标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        Map<String, List<String>> value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                        if (ObjectUtils.isNotEmpty(value)) {
                            final Set<String> input = filterModel.getLabelTypeLevelFirstList();
                            if (!value.containsKey(InsightsConstants.PERMS_QY_TAG_LEVE_FIRST_LIB_KEY)) {
                                throw new Exception("is empty");
                            }
                            final List<String> bizLevelFirstList = value.get(InsightsConstants.PERMS_QY_TAG_LEVE_FIRST_LIB_KEY);
                            final Set<String> list = bizLevelFirstList.stream().filter(input::contains).collect(Collectors.toSet());
                            if (CollUtil.isEmpty(input)) {      //  默认加载全部
                                Set<String> collect = bizLevelFirstList.stream().collect(Collectors.toSet());
                                filterModel.setLabelTypeLevelFirstList(collect);
                                filterModel.setRiskLabelTypeLevelFirstList(StringUtils.join(collect, ","));
                            } else {                          //过滤
                                filterModel.setLabelTypeLevelFirstList(new HashSet<>(CollUtil.intersection(input, list)));
                                filterModel.setRiskLabelTypeLevelFirstList(StringUtils.join(filterModel.getRiskLabelTypeLevelFirstList(), ","));
                            }
                            //重新赋值交集数据
                            log.debug("业务标签权限 {}", filterModel.getChannelIds());
                        } else {
                            throw new Exception("is empty");
                        }
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFirstList(Set.of("empty"));
            filterModel.setRiskLabelTypeLevelFirstList("empty");
            log.error("当前用户未配置有效业务标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterQYTagLevelSecondData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "业务标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        Map<String, List<String>> value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                        if (ObjectUtils.isNotEmpty(value)) {
                            final Set<String> input = filterModel.getLabelTypeLevelSecondList();
                            if (!value.containsKey(InsightsConstants.PERMS_QY_TAG_LEVE_SECOND_LIB_KEY)) {
                                throw new Exception("is empty");
                            }
                            final List<String> bizLevelSecondList = value.get(InsightsConstants.PERMS_QY_TAG_LEVE_SECOND_LIB_KEY);
                            if (CollUtil.isEmpty(input)) {      //  默认加载全部
                                Set<String> collect = bizLevelSecondList.stream().collect(Collectors.toSet());
                                filterModel.setLabelTypeLevelSecondList(collect);
                                filterModel.setRiskLabelTypeLevelSecondList(StringUtils.join(collect, ","));
                            } else {                          //过滤
                                filterModel.setLabelTypeLevelSecondList(new HashSet<>(CollUtil.intersection(input, bizLevelSecondList)));
                                filterModel.setRiskLabelTypeLevelSecondList(StringUtils.join(filterModel.getLabelTypeLevelSecondList(), ","));
                            }
                            //重新赋值交集数据
                            log.debug("业务标签权限 {}", filterModel.getChannelIds());
                        } else {
                            throw new Exception("is empty");
                        }
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelSecondList(Set.of("empty"));
            filterModel.setRiskLabelTypeLevelSecondList("empty");
            log.error("当前用户未配置有效业务标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterServiceTagLevelFirstData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "业务标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        Map<String, List<String>> value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                        if (ObjectUtils.isNotEmpty(value)) {
                            final Set<String> input = filterModel.getLabelTypeLevelFirstList();
                            if (!value.containsKey(InsightsConstants.PERMS_SERVICE_TAG_LEVE_FIRST_LIB_KEY)) {
                                throw new Exception("is empty");
                            }
                            final List<String> bizLevelFirstList = value.get(InsightsConstants.PERMS_SERVICE_TAG_LEVE_FIRST_LIB_KEY);
                            final Set<String> list = bizLevelFirstList.stream().filter(input::contains).collect(Collectors.toSet());
                            if (CollUtil.isEmpty(input)) {      //  默认加载全部
                                Set<String> collect = bizLevelFirstList.stream().collect(Collectors.toSet());
                                filterModel.setLabelTypeLevelFirstList(collect);
                                filterModel.setRiskLabelTypeLevelFirstList(StringUtils.join(collect, ","));
                            } else {                          //过滤
                                filterModel.setLabelTypeLevelFirstList(new HashSet<>(CollUtil.intersection(input, list)));
                                filterModel.setRiskLabelTypeLevelFirstList(StringUtils.join(filterModel.getLabelTypeLevelFirstList(), ","));
                            }
                            //重新赋值交集数据
                            log.debug("业务标签权限 {}", filterModel.getChannelIds());
                        } else {
                            throw new Exception("is empty");
                        }
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelFirstList(Set.of("empty"));
            filterModel.setRiskLabelTypeLevelFirstList("empty");
            log.error("当前用户未配置有效业务标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }

    public void filterServiceTagLevelSecondData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "业务标签");
        log.debug("user：{}", ServiceContextHolder.getUser());

        //当前用户渠道数据权限范围
        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Map<String, Map<String, List<String>>> modelList = (Map<String, Map<String, List<String>>>) perms.get();
                    if (CollUtil.isNotEmpty(modelList)) {
                        final Set<String> brandCodeList = filterModel.getBrandCodeList();
                        Map<String, List<String>> value = modelList.entrySet().stream().filter(e ->
                                brandCodeList.contains(e.getKey())
                        ).findFirst().orElseGet(null).getValue();
                        if (ObjectUtils.isNotEmpty(value)) {
                            final Set<String> input = filterModel.getLabelTypeLevelSecondList();
                            if (!value.containsKey(InsightsConstants.PERMS_SERVICE_TAG_LEVE_SECOND_LIB_KEY)) {
                                throw new Exception("is empty");
                            }
                            final List<String> bizLevelSecondList = value.get(InsightsConstants.PERMS_SERVICE_TAG_LEVE_SECOND_LIB_KEY);
                            if (CollUtil.isEmpty(input)) {      //  默认加载全部
                                Set<String> collect = bizLevelSecondList.stream().collect(Collectors.toSet());
                                filterModel.setLabelTypeLevelSecondList(collect);
                                filterModel.setRiskLabelTypeLevelSecondList(StringUtils.join(collect, ","));
                            } else {                          //过滤
                                filterModel.setLabelTypeLevelSecondList(new HashSet<>(CollUtil.intersection(input, bizLevelSecondList)));
                                filterModel.setRiskLabelTypeLevelSecondList(StringUtils.join(filterModel.getLabelTypeLevelSecondList(), ","));
                            }
                            //重新赋值交集数据
                            log.debug("业务标签权限 {}", filterModel.getChannelIds());
                        } else {
                            throw new Exception("is empty");
                        }
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }

        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setLabelTypeLevelSecondList(Set.of("empty"));
            filterModel.setRiskLabelTypeLevelSecondList("empty");
            log.error("当前用户未配置有效业务标签数据 user:{}", ServiceContextHolder.getUser());
        }
    }


    public void filterRegionData(CommonFilterModel filterModel, boolean admin) {
        log.debug("进行权限过滤 {}", "区域");
        log.debug("user：{}", ServiceContextHolder.getUser());
        if (admin) {
            log.debug("超级管理员不进行权限校验，直接获取入参权限");
            return;
        }
        Set<String> areaIdList = Collections.synchronizedSet(new HashSet<>());
        final Map<String, Map<String, List<String>>> brandMap = ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY);
        brandMap.entrySet().stream().filter(e -> e.getKey().equals(filterModel.getBrandCodeList().stream().findAny().get())).forEach(e -> {
            Map<String, List<String>> value = e.getValue();
            value.entrySet().stream().filter(k -> k.getKey().equals(InsightsConstants.PERMS_REGION_KEY)).forEach(k -> {
                areaIdList.addAll(k.getValue());
            });
        });
        //当前用户渠道数据权限范围
//        Optional<Object> perms = Optional.ofNullable(ServiceContextHolder.getUser().getBusinessPermissions().getValue(InsightsConstants.PERMS_BRAND_KEY));
        Optional<Object> perms = Optional.ofNullable(areaIdList);
        try {
            if (admin) {
                log.debug("超级管理员不进行权限校验，直接获取入参权限");
            } else {
                if (perms.isPresent()) {
                    Set<String> values = (Set<String>) perms.get();
                    if (CollUtil.isNotEmpty(values)) {
                        log.debug("区域权限数据：{}", values);
                        if (CollUtil.isEmpty(filterModel.getAreaIds())) {
                            filterModel.setAreaIds(values);
                            return;
                        }
                        //使用当前用户已授权信息，校验前端传入的数据
                        //当前用户已授权信息
                        if (CollUtil.isEmpty(values)) {      //  默认加载全部
                            throw new Exception("[区域]前端传入的数据异常，".concat(String.valueOf(filterModel.getAreaIds())));
                        } else {
                            //校验前端传入的数据
                            filterModel.setAreaIds(new HashSet<>(CollUtil.intersection(values, filterModel.getAreaIds())));
                        }
                        //重新赋值交集数据
                        log.debug("区域权限 {}", filterModel.getAreaIds());
                    } else {
                        throw new Exception("is empty");
                    }
                } else {
                    throw new Exception("is empty");
                }
            }
        } catch (Exception e) {
            //设置默认空值，在数据查询时将无法查询到满足条件数据
            filterModel.setAreaIds(Set.of("empty"));
            log.error(e.getMessage(), e);
            log.error("当前用户未配置有效区域数据 user:{}", ServiceContextHolder.getUser());
        }
    }


    /**
     * 计算指标-NSR
     * （正面+中性-负面）/总观点数*100%
     *
     * @param param
     * @return
     */
    public IndicatorsNSRResultModel computedIndicatorsNSR(IndicatorsNSRParamModel param) {
        final String expression = config.getIndicatorsNSR();
        log.debug("NSR:{}", expression);

        Assert.isTrue(StrUtil.isNotBlank(expression), "expression cannot be empty");
        Assert.isTrue(ObjUtil.isNotNull(param.getNegative()), "getNegative cannot be empty");
        Assert.isTrue(ObjUtil.isNotNull(param.getPositive()), "getPositive cannot be empty");
        Assert.isTrue(ObjUtil.isNotNull(param.getNeutral()), "getNeutral cannot be empty");

        Map<String, Object> variable = new HashMap<>();
        variable.put("positive", param.getPositive());
        variable.put("neutral", param.getNeutral());
        variable.put("negative", param.getNegative());

        BigDecimal rs = (BigDecimal) ExpressionUtil.eval(expression, variable);

        //NSR计算结果
        return IndicatorsNSRResultModel.builder().value(rs.setScale(2, BigDecimal.ROUND_HALF_UP)).build();
    }


    /**
     * 综合体验指数表情
     *
     * @return
     */
    public int computedIndicatorsEmoji(String clientId, String brandCode, BigDecimal val) {
        //读取数据并放入参数 IndicatorsEmojiParamModel
        IndicatorsEmojiParamVo threshold = reportThresholdService.findThreshold(clientId, brandCode);
        if (ObjectUtils.isNotEmpty(threshold)) {
            IndicatorsEmojiParamModel emojiParamModel = IndicatorsEmojiParamModel.builder().build();
            BeanUtils.copyProperties(threshold, emojiParamModel);
            return this.computedIndicatorsEmoji(emojiParamModel, val);
        }
//        return this.computedIndicatorsEmoji(IndicatorsEmojiParamModel.builder().build(), val);
        return -1;
    }

    public int computedIndicatorsEmoji(IndicatorsEmojiParamModel param, BigDecimal val) {
        if (val.compareTo(param.getExcellentS()) >= 0 && val.compareTo(param.getExcellentE()) <= 0) {
            return 4;
        } else if (val.compareTo(param.getGoodS()) > 0 && val.compareTo(param.getGoodE()) <= 0) {
            return 3;
        } else if (val.compareTo(param.getImprovementS()) > 0 && val.compareTo(param.getImprovementE()) <= 0) {
            return 2;
        } else if (val.compareTo(param.getPoorS()) >= 0 && val.compareTo(param.getPoorE()) <= 0) {
            return 1;
        } else ;
        return -1;
    }


    public static void main(String[] args) {

//        System.out.println(CollUtil.intersection(Arrays.asList("A", "B"), Arrays.asList("A", "c")));
        BigDecimal bigDecimal = BigDecimal.valueOf(-100);
        BigDecimal bigDecimal1 = BigDecimal.valueOf(30);
        BigDecimal bigDecimal2 = BigDecimal.valueOf(30.0);
        if (bigDecimal.compareTo(bigDecimal2) >= 0) {

        }
    }
}
