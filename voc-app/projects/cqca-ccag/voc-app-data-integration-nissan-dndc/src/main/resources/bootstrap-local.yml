server.port: 8080

spring:
  cloud.nacos:
    config:
      enabled: false
    discovery:
      enabled: false
  config:
    location: classpath:${spring.profiles.active}/common.yml,classpath:${spring.profiles.active}/common-redis.yml,classpath:${spring.profiles.active}/common-swagger.yml,classpath:${spring.profiles.active}/projects/nissan-dndc/voc-nissan-dndc-data-integration-starrocks.yml,classpath:${spring.profiles.active}/projects/nissan-dndc/voc-nissan-dndc-data-integration-kafka.yml,classpath:${spring.profiles.active}/projects/nissan-dndc/voc-nissan-dndc-data-integration-xxljob.yml,classpath:${spring.profiles.active}/projects/nissan-dndc/voc-nissan-dndc-data-integration.yml


logging:
  config: classpath:${spring.profiles.active}/logback-spring.xml
