package com.meicloud.voc.group.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meicloud.voc.category.dto.SaveGroupCategoryParam;
import com.meicloud.voc.group.dto.GroupInfoBo;
import com.meicloud.voc.group.entity.Group;
import com.meicloud.voc.group.entity.GroupDateDetail;
import com.meicloud.voc.user.dto.GroupDateDetailInfoBo;
import com.meicloud.voc.user.dto.SaveGroupParam;

import java.util.List;

public interface IGroupService extends IService<Group> {
	/**
	 * 获取分组的时间
	 * 
	 * @param companyId
	 * @param userAccount
	 * @return
	 */
	GroupDateDetail getGroupDateByCompanyAndUser(int companyId, String userAccount);

	/**
	 * 获取租户分组列表
	 * 
	 * @param companyId
	 * @return
	 */
	List<GroupInfoBo> getCompanyGroup(int companyId);

	/**
	 * 获取用户分组列表
	 * 
	 * @param companyId
	 * @param userAccount
	 * @return
	 */
	List<GroupInfoBo> getUserGroup(int companyId, String userAccount);

	/**
	 * 保存分组菜单
	 * 
	 * @param groupId
	 * @param menuIds
	 */
	void saveGroupMenu(int groupId, List<String> menuIds);

	/**
	 * 保存分组品类
	 * 
	 * @param groupId
	 * @param saveGroupCategoryParam
	 */
	void saveGroupCategory(int groupId, SaveGroupCategoryParam saveGroupCategoryParam);

	/**
	 * 保存用户组（角色）
	 * 
	 * @param saveGroupParam
	 * @param companyId
	 */
	void saveGroup(SaveGroupParam saveGroupParam, int companyId);

	/**
	 * 获取用户组可查询日期范围
	 * 
	 * @param groupId
	 * @return
	 */
	GroupDateDetailInfoBo getGroupDateDetail(int groupId);
}
