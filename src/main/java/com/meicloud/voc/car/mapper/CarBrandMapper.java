package com.meicloud.voc.car.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meicloud.voc.car.entity.CarBrand;

//@DS("datachangan")
public interface CarBrandMapper extends BaseMapper<CarBrand> {
	/**
	 * 获取搜索车辆品牌列表
	 * @param levelId
	 * @param brandIds
	 * @param defBrandIds
	 * @return
	 */
	List<CarBrand> getSearchCarBrands(@Param("brandIds") List<Integer> brandIds, 
			@Param("brandName") String brandName,  @Param("nature") String nature, @Param("hot")String hot);

}
