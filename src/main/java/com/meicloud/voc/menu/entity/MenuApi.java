package com.meicloud.voc.menu.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <p>
 * 菜单
 * </p>
 *
 * <AUTHOR>
 * @since 2021-03-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("t_menu_api")
public class MenuApi implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 菜单id
     */
    private String menuId;

    /**
     * 接口地址
     */
    private String apiUrl;
    /**
     * 1:可用；0：不可用
     */

    private Integer isEnable;

    /**
     * 创建时间
     */
    private String createTime;
}
