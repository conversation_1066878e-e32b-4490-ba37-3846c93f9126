package com.meicloud.voc.manage.questionnaireimport.service.impl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meicloud.voc.common.enums.ColumnEnum;
import com.meicloud.voc.common.listener.ExcelDataListener;
import com.meicloud.voc.common.utils.DateUtil;
import com.meicloud.voc.manage.enums.JobStatusEnum;
import com.meicloud.voc.manage.questionnaireimport.dto.QuestionnaireImportDto;
import com.meicloud.voc.common.exception.ServiceException;
import com.meicloud.voc.common.dto.PageList;
import com.meicloud.voc.common.dto.QueryPageDto;
import com.meicloud.voc.common.utils.BeanCopyUtils;
import com.meicloud.voc.manage.questionnaireimport.dto.QuestionnaireImportInfoDto;
import com.meicloud.voc.manage.questionnaireimport.dto.QuestionnaireImportSearchParamsDto;
import com.meicloud.voc.manage.questionnaireimport.entity.QuestionnaireImport;
import com.meicloud.voc.manage.questionnaireimport.handler.QuestionnaireImportFailFileHandler;
import com.meicloud.voc.manage.questionnaireimport.mapper.IQuestionnaireImportMapper;
import com.meicloud.voc.manage.questionnaireimport.service.IQuestionnaireImportService;
import com.meicloud.voc.manage.questionnaireimportdetail.entity.QuestionnaireImportDetail;
import com.meicloud.voc.manage.questionnaireimportdetail.service.impl.QuestionnaireImportDetailServiceImpl;
import com.meicloud.voc.manage.service.QuestionnaireImportService;
import com.meicloud.voc.common.utils.PageListUtils;
import com.meicloud.voc.common.constance.Status;
import com.meicloud.voc.selfAnalysis.service.IStandardKeywordService;
import com.meicloud.voc.utils.EncryptUtil;
import com.meicloud.voc.utils.FileUtil;
import com.meicloud.voc.utils.oss.AliyunOSSUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 线下问卷导入批次表(QuestionnaireImport)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-28 10:54:02
 */
@Service("questionnaireImportService")
public class QuestionnaireImportServiceImpl extends ServiceImpl<IQuestionnaireImportMapper, QuestionnaireImport> implements IQuestionnaireImportService, QuestionnaireImportService {
    private static final Logger logger = LoggerFactory.getLogger(QuestionnaireImportServiceImpl.class);

    @Value("${file.tmp.path}")
    private String tmpPath;
    @Autowired
    private QuestionnaireImportDetailServiceImpl questionnaireImportDetailService;

    @Autowired
    private IStandardKeywordService standardKeywordService;

    @Autowired
    private AliyunOSSUtil aliyunOSSUtil;

    /**
     * 通过ID查询单条数据
     *
     * @param dataId 主键
     * @return 实例对象
     */
    @Override
    public QuestionnaireImportDto findById(Long dataId) {
        return BeanCopyUtils.copyProperties(this.getById(dataId), QuestionnaireImportDto.class);
    }


    /**
     * 分页查询
     *
     * @param queryDto 筛选条件
     * @return 查询结果
     */
    @Override
    public PageList<QuestionnaireImportDto> page(QueryPageDto<QuestionnaireImportSearchParamsDto> queryDto) {
        QuestionnaireImportSearchParamsDto searchParams = queryDto.getSearchParams();
        IPage<QuestionnaireImport> page = new Page<>();
        page.setSize(queryDto.getSize());
        page.setCurrent(queryDto.getCurrent());

        QueryWrapper<QuestionnaireImport> wrapper = new QueryWrapper<>();
        wrapper.eq(ColumnEnum.STATUS.getValue(), Status.NORMAL);
        wrapper.orderByDesc(ColumnEnum.W_INSERT_DT.getValue());
        page = page(page, wrapper);
        PageList<QuestionnaireImportDto> pageList = PageListUtils.getPageListByIPage(page, QuestionnaireImportDto.class);
        return pageList;
    }

    /**
     * 新增数据
     *
     * @param questionnaireImportDto 实例对象
     * @return 实例对象
     */
    @Override
    public QuestionnaireImportDto create(QuestionnaireImportDto questionnaireImportDto) {
        QuestionnaireImport questionnaireImport = BeanCopyUtils.copyProperties(questionnaireImportDto, QuestionnaireImport.class);
        questionnaireImport.setWInsertDt(new Date());
        questionnaireImport.setWPdateDt(new Date());
        if (this.save(questionnaireImport)) {
            return BeanCopyUtils.copyProperties(questionnaireImport, QuestionnaireImportDto.class);
        } else {
            throw new ServiceException("新增数据失败");
        }
    }

    /**
     * 修改数据
     *
     * @param questionnaireImportDto 实例对象
     * @return 实例对象
     */
    @Override
    public QuestionnaireImportDto update(QuestionnaireImportDto questionnaireImportDto) {
        QuestionnaireImport questionnaireImport = BeanCopyUtils.copyProperties(questionnaireImportDto, QuestionnaireImport.class);

        if (this.updateById(questionnaireImport)) {
            return BeanCopyUtils.copyProperties(questionnaireImport, QuestionnaireImportDto.class);
        } else {
            throw new ServiceException("更新数据失败");
        }
    }

    /**
     * 通过主键删除数据
     *
     * @param dataId 主键
     * @return 是否成功
     */
    @Transactional
    @Override
    public boolean deleteById(Long dataId) {
        return this.removeById(dataId);
    }

    /**
     * 通过主键设置数据状态为无效
     *
     * @param dataId 主键
     * @return 是否成功
     */
    @Transactional
    @Override
    public boolean removeLogicalById(Long dataId) {
        return this.update(new UpdateWrapper<QuestionnaireImport>().eq("data_id", dataId).set("status", Status.DELETE));
    }

    @Override
    @Async("fileExecutor")
    public void handleUploadFile(QuestionnaireImportDto questionnaireImportDto, MultipartFile uploadFile) {
        QuestionnaireImport questionnaireImport = BeanCopyUtils.copyProperties(questionnaireImportDto, QuestionnaireImport.class);

        List<QuestionnaireImportInfoDto> importList = null;
        try {
            ExcelDataListener<QuestionnaireImportInfoDto> listener = new ExcelDataListener<QuestionnaireImportInfoDto>();

            EasyExcel.read(uploadFile.getInputStream())
                    .registerReadListener(listener)
                    .head(QuestionnaireImportInfoDto.class)
                    .headRowNumber(2)
                    .sheet()
                    .doRead();
            importList = listener.getList();

        } catch (IOException e) {
            logger.error("读取上传文件失败", e);
            questionnaireImport.setJobStatus(JobStatusEnum.FAIL.getValue());
            questionnaireImport.setErrorMsg("读取上传文件失败: "+ e.getMessage());
            this.saveOrUpdate(questionnaireImport);
            throw new RuntimeException(e);
        }

        questionnaireImport.setJobStatus(JobStatusEnum.PROCESS.getValue());
        this.saveOrUpdate(questionnaireImport);

        List<QuestionnaireImportDetail> questionnaireImportDetails = BeanCopyUtils.copyProperties(importList, QuestionnaireImportDetail.class);
        questionnaireImportDetails.forEach(x -> {
            x.setBatchId(questionnaireImport.getDataId());
            x.setWInsertDt(new Date());
            x.setWPdateDt(new Date());
            x.setImportDate(questionnaireImport.getImportDate());
        });

        int size = questionnaireImportDetails.size();
        List<QuestionnaireImportDetail> failImportList = new ArrayList<>();

        Long success = Long.valueOf(0);
        Long fail = Long.valueOf(0);
        for (int i = 0; i < size; i++) {
            try {
                if (checkImportData(questionnaireImportDetails.get(i)) && questionnaireImportDetailService.save(questionnaireImportDetails.get(i))) {
                    success += 1;
                } else {
                    fail += 1;
                    failImportList.add(questionnaireImportDetails.get(i));
                }
            } catch (Exception e) {
                log.error(e.getMessage(),e);;
                logger.error("批量插入数据失败", e);
                fail +=1;
                failImportList.add(questionnaireImportDetails.get(i));
            }

            if((success + fail) % 100 == 0) {
                questionnaireImport.setSuccessCount(success);
                questionnaireImport.setFailCount(fail);
                this.updateById(questionnaireImport);
            }
        }

        questionnaireImport.setSuccessCount(success);
        questionnaireImport.setFailCount(fail);
        this.updateById(questionnaireImport);

        // 保存失败的数据
        if(!failImportList.isEmpty()) {
            questionnaireImport.setJobStatus(JobStatusEnum.PART_FINISH.getValue());
            try {
                String fileName = uploadFile.getOriginalFilename();
                String[] fileNameSplit = fileName.split("\\.");
                String newFileName = fileNameSplit[0]+ "_"+ System.currentTimeMillis() + "_失败数据.xlsx";
                File file = new File(tmpPath, newFileName);
                List<QuestionnaireImportInfoDto> failInfoList = BeanCopyUtils.copyProperties(failImportList, QuestionnaireImportInfoDto.class);
                EasyExcel.write(file)
                        .autoCloseStream(true)
                        .relativeHeadRowIndex(1)
                        .registerWriteHandler(new QuestionnaireImportFailFileHandler())
                        .sheet("失败数据")
                        .head(QuestionnaireImportInfoDto.class)
                        .doWrite(failInfoList);
                String failFile = aliyunOSSUtil.ossUploadFile(tmpPath + File.separator + newFileName);
                questionnaireImport.setFailFileLink(failFile);
            } catch (Exception e) {
                logger.error("保存失败数据失败", e);
            }
        } else {
            questionnaireImport.setJobStatus(JobStatusEnum.FINISH.getValue());
        }

        this.saveOrUpdate(questionnaireImport);
    }

    @Override
    public void downloadTemplate(HttpServletResponse response) {
        FileUtil.downloadLocalFile(response, "exportTemplate/questionnaireTemplate.xlsx", "线下调研问卷导入模板.xlsx");
    }

    /**
     * 下载数据
     *
     * @param
     * @param response
     */
    @Override
    public void download(String bucket, String fileName, HttpServletResponse response) {
        // 文件传输到前端
        aliyunOSSUtil.downloadFile(bucket, fileName, response);
    }

    /**
     * 检查导入的数据
     * @param questionnaireImportDetail
     * @return
     */
    private boolean checkImportData(QuestionnaireImportDetail questionnaireImportDetail) {
        // 导入数据问题不能为空
        if(StringUtils.isBlank(questionnaireImportDetail.getQuestion())) {
            return false;
        }

        // 导入数据答案不能为空
        if(StringUtils.isBlank(questionnaireImportDetail.getAnswer())) {
            return false;
        }

        // 导入数据答题时间
        if(StringUtils.isBlank(questionnaireImportDetail.getAnswerTime())
                || !(DateUtil.isValidDate(questionnaireImportDetail.getAnswerTime(), DateUtil.YYYY_M_D_SMALL)
                || DateUtil.isValidDate(questionnaireImportDetail.getAnswerTime(), DateUtil.YYYY_MM_DD_SMALL))) {
            return false;
        }

        // 导入数据标准关键词不能为空
        if(StringUtils.isBlank(questionnaireImportDetail.getStandardKeyword())
           || !standardKeywordService.isExistStandardKeyword(questionnaireImportDetail.getStandardKeyword())) {
            return false;
        }

        if(DateUtil.isValidDate(questionnaireImportDetail.getAnswerTime(), DateUtil.YYYY_M_D_SMALL)){
               questionnaireImportDetail.setAnswerTime(DateUtil.convertDate(questionnaireImportDetail.getAnswerTime(), DateUtil.YYYY_M_D_SMALL));
        } else if (DateUtil.isValidDate(questionnaireImportDetail.getAnswerTime(), DateUtil.YYYY_MM_DD_SMALL)) {
            questionnaireImportDetail.setAnswerTime(DateUtil.convertDate(questionnaireImportDetail.getAnswerTime(), DateUtil.YYYY_MM_DD_SMALL));
        }

        return true;
    }
}
