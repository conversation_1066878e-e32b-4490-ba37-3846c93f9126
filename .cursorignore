# Compiled class files
*.class

# Maven build directory
target/
**/target/

# Logs
logs/
*.log
*.log.*

# Package files
*.jar
*.war
*.ear
*.zip
*.tar.gz

# IntelliJ files
.idea/
*.iml
*.ipr
*.iws
out/

# Eclipse files
.classpath
.project
.settings/
bin/

# VS Code
.vscode/

# Node modules (if any frontend code)
node_modules/

# Gradle
.gradle/
build/

# Temp files
*.tmp
*.temp

# Large binary files
*.bin
*.dat
*.exe
*.dll

# Mac OS
.DS_Store

# Windows
Thumbs.db
Desktop.ini 

# VOC Services (except report services)
voc-service-ai-workflow/
voc-service-analysis/
voc-service-bizlogs/
voc-service-common/
voc-service-components/
voc-service-config/
voc-service-data-integration/
voc-service-insights/
voc-service-model/
voc-service-security/
voc-service-third/

# VOC Apps (except report apps)
voc-app/voc-app-ai-workflow/
voc-app/voc-app-analysis/
voc-app/voc-app-auth/
voc-app/voc-app-data-integration/
voc-app/voc-app-insights/
voc-app/voc-app-model/
voc-app/projects/cqca-ccag/voc-app-data-integration-nissan-dndc/

# Agent and other directories
agent/ 
