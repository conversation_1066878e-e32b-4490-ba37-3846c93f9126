package com.voc.service.model.web;

import com.voc.service.common.response.Result;
import com.voc.service.logs.annotation.AutoLog;
import com.voc.service.model.api.IModelMilvusResultDataService;
import com.voc.service.model.utils.Milvus;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @创建者: fanrong
 * @创建时间: 2024/9/13 下午2:40
 * @描述:
 **/
@RestController
@RequestMapping("/")
@Slf4j
public class OnnxRunTimeController {
    @Autowired
    IModelMilvusResultDataService onnxService;
    @Autowired
    Milvus milvus;
    @AutoLog(value = "根据观点计算Embedding")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "根据观点计算Embedding")
    @PostMapping("/getEmbedding")
    Result<?>  getOnnxRuntimeEmbeddingData(@RequestBody List<String> opinionList) {
        Map<String, List<Float>> onnxRuntimeEmbeddingData = onnxService.getOnnxRuntimeEmbeddingData(opinionList);
        return Result.OK(onnxRuntimeEmbeddingData);
    }

    @AutoLog(value = "获取相似数据")
    @Parameter(name = "Authorization", in = ParameterIn.HEADER, required = true, description = "Bearer [token]")
    @Operation(summary = "获取相似数据")
    @PostMapping("/getSimilarValues")
    Result<?>  getSimilarValues(String subjectAsppect,String milvus_collection_name) {
        Map<String, List<Float>> onnxvalue = onnxService.getOnnxRuntimeEmbeddingData(Collections.singletonList(subjectAsppect));
        return Result.OK( milvus.search(onnxvalue.get(subjectAsppect), milvus_collection_name, List.of("id", "opinion"), "embedding", 5));
    }
}
